@extends('layouts.admin.master')
@section('title', 'Dashboard')
@section('section')
    <section class="auth_section">
        <div class="container">

            <div class="auth_container">
                <div class="auth_wrapper">
                    <div class="text-center mb-4">
                        <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/lock-bg-icon.png') }}"
                            alt="search icon">
                    </div>
                    <h1 class="auth_title text-center">Change Password</h1>
                    <p class="auth_note text-center mt-2">That’s it. Setup your new password</p>

                    <form id="create_new_pass_form" method="POST" action="{{ route(getRouteAlias() . '.passwordUpdate') }}"
                        enctype="multipart/form-data">
                        @csrf


                        <div class="field-group mt-5">
                            <label class="label mb-2" for="new_password">Current Password <span
                                    class="steric">*</span></label>
                            <div class="password-wrapper">
                                <input class="form-control input" type="password" name="current_password"
                                    id="current_password" placeholder="Enter current password">
                                <i class="toggle-password fa fa-eye"></i>
                            </div>
                            @if ($errors->has('current_password'))
                                <div class="laravel_error">{{ $errors->first('current_password') }}</div>
                            @endif
                        </div>


                        <div class="field-group mt-4">
                            <label class="label mb-2" for="new_password">Password <span class="steric">*</span></label>
                            <div class="password-wrapper">
                                <input class="form-control input" type="password" name="password" id="password"
                                    placeholder="Enter password">
                                <i class="toggle-password fa fa-eye"></i>
                            </div>
                            @if ($errors->has('password'))
                                <div class="laravel_error">{{ $errors->first('password') }}</div>
                            @endif
                        </div>

                        <div class="field-group mt-4">
                            <label class="label mb-2" for="conf_new_password">Confirm Password <span
                                    class="steric">*</span></label>
                            <div class="password-wrapper">
                                <input class="form-control input" type="password" name="password_confirmation"
                                    id="password_confirmation" placeholder="Enter password">
                                <i class="toggle-password fa fa-eye"></i>
                            </div>
                            @if ($errors->has('password_confirmation'))
                                <div class="laravel_error">{{ $errors->first('password_confirmation') }}</div>
                            @endif
                        </div>

                        <button class="btn primaryblue w-100 mt-5" type="submit">Update Password</button>
                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection
