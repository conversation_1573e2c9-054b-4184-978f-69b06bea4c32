<html>

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <title>Estimate Invoice</title>
    <link href="https://db.onlinewebfonts.com/c/1110de198c12e958e543b69a2d957585?family=<PERSON><PERSON><PERSON>+<PERSON><PERSON>" rel="stylesheet">

    <style>
        /** Define the margins of your page **/
        @page {
            margin: 0px 0px;
        }

        header {
            position: fixed;
            top: 0px;
            left: 0px;
            right: 0px;
            height: 50px;
            font-size: 20px !important;

            /** Extra personal styles **/
            color: white;
            text-align: center;
            line-height: 35px;
        }

        footer {
            position: fixed;
            bottom: 0px;
            left: 0px;
            right: 0px;
            height: 50px;
            font-size: 20px !important;

            /** Extra personal styles **/
            color: white;
            text-align: center;
            line-height: 35px;
        }


        /* @font-face {
            font-family: '<PERSON><PERSON><PERSON>ie';
            src: url({{ url('fonts/Reenie/ReenieBeanie-Regular.ttf') }}) format("truetype");
            font-weight: 400;
            font-style: normal;
        } */
        @font-face {
            font-family: 'Poppins';
            src: url({{ storage_path('fonts/Poppins/Poppins-Regular.ttf') }}) format("truetype");
            font-weight: 400;
            font-style: normal;
        }

        @font-face {
            font-family: 'Poppins';
            src: url({{ storage_path('fonts/Poppins/Poppins-Bold.ttf') }}) format("truetype");
            font-weight: 700;
            font-style: normal;
        }

        @font-face {
            font-family: 'Poppins';
            src: url({{ storage_path('fonts/Poppins/Poppins-SemiBold.ttf') }}) format("truetype");
            font-weight: 600;
            font-style: normal;
        }

        @font-face {
            font-family: 'Poppins';
            src: url({{ storage_path('fonts/Poppins/Poppins-Medium.ttf') }}) format("truetype");
            font-weight: 500;
            font-style: normal;
        }


        .w-100 {
            width: 100%;
        }

        .w-70 {
            width: 70%;
        }

        .w-50 {
            width: 50%;
        }

        .w-30 {
            width: 30%;
        }

        .float-left {
            float: left;
        }

        .float-right {
            float: right;
        }

        .mt-28 {
            margin-top: 28px;
        }

        .text-left {
            text-align: left;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        body {
            margin: 0;
            padding: 0;
            /* overflow: hidden; */
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
        }

        .bottom_bar {
            position: fixed;
            width: 100%;
            bottom: 0px;
            left: 0px;
            z-index: 20;
        }

        .top_bar .primary_header,
        .bottom_bar .bar {
            height: 16px;
            width: 100%;
            background: {{ $primary_color }};
            position: relative;
        }

        .top_bar .primary_header_notch {
            transform: rotate(41.59deg);
            position: absolute;
            right: -59px;
            top: -17px;
            background: {{ $secondary_color }};
        }

        .top_bar .primary_header_notch .notch_1 {
            height: 86px;
            width: 188px;
            background: {{ $secondary_color }};
            /* background: #003366; */
        }

        .top_bar .primary_header_notch .notch_2 {
            height: 12px;
            width: 188px;
            background: {{ $primary_color }};
            /* background: #FE9B6A; */
        }

        .header {
            /* margin-top: 28px; */
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 153px;
        }

        .header .proposal_no {
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 100%;
            text-transform: capitalize;
            color: #707683;
        }

        .header .proposal_no .value {
            color: #192a3e;
        }

        .placeholder-text {
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 100%;
            text-transform: capitalize;
            color: #707683;
        }

        .title_name {
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            text-transform: capitalize;
            color: #003366;
        }

        .address,
        .zip_code {
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 18px;
            text-transform: capitalize;
            color: #192a3e;
        }

        .main_heading {
            font-family: "Poppins", sans-serif;
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            text-transform: capitalize;
            color: #192a3e;
        }

        .spacing-0 {
            padding: 0px !important;
            margin: 0px !important;
        }

        .mt-4 {
            margin-top: 4px;
        }

        .mb-12 {
            margin-bottom: 12px;
        }

        .mt-16 {
            margin-top: 16px;
        }

        .mt-24 {
            margin-top: 24px;
        }

        .mt-49 {
            margin-top: 49px;
        }

        .vertical-top {
            vertical-align: top;
        }

        .vertical-middle {
            vertical-align: middle;
        }

        /* table style */

        .custom_datatable {
            box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px,
                rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
            background: #ffffff;
            border-radius: 8px;
        }

        .table_wrapper {
            border: 1px solid #e7e7e7;
            border-radius: 8px;
            overflow: hidden;
        }

        .custom_datatable thead th {
            /* padding: 16px !important; */
            vertical-align: middle;
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 22px;
            color: #192a3e;

            background: #dcf2ff;
            border-bottom: none !important;
            white-space: nowrap;
            user-select: none;
            margin: 0px;
            padding-left: 12px;
            padding-bottom: 12px;
            padding-right: 12px;
        }

        .custom_datatable thead th:first-child {
            border-top-left-radius: 8px;
        }

        .custom_datatable thead th:last-child {
            border-top-right-radius: 8px;
        }

        .custom_datatable tbody td {
            padding: 14px !important;
            vertical-align: middle;
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
            font-style: normal;
            font-weight: 400;
            font-size: 10px;
            line-height: 10px;
            color: #192a3e;
            border: none !important;
            word-wrap: break-word;
            word-break: break-word;

            margin: 0px;
            /* padding-left: 12px;
            padding-bottom: 12px;
            padding-right: 12px; */
        }

        .custom_datatable tbody tr:nth-child(even) td {
            background-color: #f9f9f9 !important;
        }

        .panel {
            background: #ffffff;
            border: 1px solid #e7e7e7;
            border-radius: 8px;
            padding: 16px;
        }

        .vertical_line {
            border: none;
            border-bottom: 1px solid #e7e7e7;
        }

        .leading-16 {
            line-height: 16px;
        }

        .mb-24 {
            margin-bottom: 24px;
        }

        .mb-16 {
            margin-bottom: 16px;
        }

        .mb-8 {
            margin-bottom: 8px;
        }

        .page-break {
            page-break-before: always;
        }

        .total_payable .title {
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 18px;
            color: #192a3e;
        }

        .total_payable .value {
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
            font-style: normal;
            font-weight: 400;
            font-size: 24px;
            line-height: 40px;
            color: #003366;
        }

        .approval_title {
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 100%;
            text-transform: capitalize;
            color: #192a3e;
        }

        .approval_items .sign {
            margin-right: 16px;
        }

        .approval_items .signature {
            width: 100%;
            border-top: 1px dashed #90a0b7;
        }

        .approval_items .sign_title {
            font-family: "Poppins", sans-serif;
            /* font-family:'Poppins'; */
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 100%;
            text-transform: capitalize;
            color: #707683;
        }

        .mt-32 {
            margin-top: 32px;
        }

        .content_wrapper {
            padding: 44px 40px
        }

        .approval_items .signature_text {
            font-family: "Reenie Beanie", sans-serif;
            font-style: normal;
            font-weight: 400;
            font-size: 35px;
            line-height: 12px;
            color: #192A3E;
        }

        .sign-date {
            font-size: 12px
        }
    </style>
</head>

<body>
    <!-- Define header and footer blocks before your content -->
    <header>
        <div class="top_bar c_header">
            <div class="primary_header"></div>

            <div class="primary_header_notch">
                <div class="notch_1"></div>
                <div class="notch_2"></div>
            </div>
        </div>
    </header>

    <footer class="bottom_bar mt-24 ">
        <table class="table" style="width:100%;">
            <tr>
                <td
                    style="
                    width:50%;
                    text-align:left;
                    font-family: 'Poppins';
                    font-style: normal;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 22px;
                    color: #90A0B7;
                    padding-left:44px;">
                    Powered by <span style="color: #003366;text-transform: uppercase;">{{ config('app.name') }}</span>
                </td>
                <td
                    style="
                    width:50%;
                    text-align:right;
                    font-family: 'Poppins';
                    font-style: normal;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 22px;
                    text-transform: uppercase;
                    color: #90A0B7;
                    padding-right:44px;">
                    Confidential</td>
            </tr>
        </table>
        <div class="bar"></div>

        {{-- <br /><br />
        <footer class="bottom_bar mt-24 c_footer">
            <table class="table" style="width:100%;">
                <tr>
                    <td
                        style="
                        width:50%;
                        text-align:left;
                        font-family: 'Poppins';
                        font-style: normal;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 22px;
                        color: #90A0B7;
                        padding-left:44px;">
                        Powered by <span style="color: #003366;text-transform: uppercase;">{{ config('app.name') }}</span>
                    </td>
                    <td
                        style="
                        width:50%;
                        text-align:right;
                        font-family: 'Poppins';
                        font-style: normal;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 22px;
                        text-transform: uppercase;
                        color: #90A0B7;
                        padding-right:44px;">
                        Confidential</td>
                </tr>
            </table>
            <div class="bar"></div>
        </footer> --}}
    </footer>


    <!-- <div class="content_wrapper">
        <div class="header w-100">
            <table class="table w-100">
                <tr>
                    <th class="w-50 float-left text-left">
                        <img loading="lazy" height="47px" src="{{ $logo }}" alt="site logo" />
                    </th>
                    <th class="w-50 float-left">
                        <p class="proposal_no w-100">
                            PROPOSAL# <span class="value">{{ $estimate->job_no ?? '' }}</span>
                        </p>
                    </th>
                </tr>
            </table>
        </div>

        <div style="clear: both"></div> -->

        <!-- <div class="w-100 mt-16">
            <table class="table w-100">
                <tr>
                    <th class="float-left spacing-0 vertical-top" style="width: 33%">
                        <p class="placeholder-text text-left spacing-0">
                            Recipient
                        </p>
                        <h2 class="title_name text-left spacing-0 mt-4">
                            {{ $estimate->client->first_name . ' ' . $estimate->client->last_name }}
                        </h2>
                    </th>
                    <th class="float-left spacing-0 vertical-top" style="width: 33%">
                        <p class="placeholder-text text-left spacing-0 mt-4">
                            Job site
                        </p>
                        <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                            {{ $estimate->propertyAddress?->address1 }}
                        </p>
                    </th>
                    <th class="float-left spacing-0 vertical-top" style="width: 33%">
                        <p class="placeholder-text text-left spacing-0">
                            Project name
                        </p>
                        <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                            {{ $estimate->project_name }}
                        </p>
                    </th>
                </tr>
            </table>
        </div>

        <div style="clear: both"></div>
        <div class="w-100 mt-16">
            <table class="table w-100">
                <tr>
                    @if ($estimate->property_address_property_name)
                        <td class="spacing-0 vertical-top" style="width: 33%">
                            <p class="placeholder-text text-left spacing-0">
                                Property Name
                            </p>
                            <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                {{ $estimate->property_address_property_name }}
                            </p>
                        </td>
                    @endif
                    @if ($estimate->client?->company_name)
                        <td colspan="2" class="spacing-0 vertical-top" style="width: 66%">
                            <p class="placeholder-text text-left spacing-0">
                                Company Name
                            </p>
                            <p class="address text-left spacing-0 mt-4" style="max-width:165px;">
                                {{ $estimate->client?->company_name }}
                            </p>
                        </td>
                    @endif
                </tr>
            </table>
        </div>

        <div style="clear: both"></div> -->

        <!-- @php
            $sales_tax = 0;
            if (isset($organization_id)) {
                $sales_tax = getSaleTax($organization_id);
            } else {
                $sales_tax = getSaleTax();
            }
            $chunkSize = 14;
        @endphp
        <div class="w-100 mt-16">
            <table class="table w-100">
                <tr>
                    <td class="w-100 spacing-0 vertical-top">
                        <p class="main_heading text-center spacing-0">
                            SCOPE OF WORK
                        </p>
                    </td>
                </tr>
                <tr>
                    <td class="w-100 spacing-0 vertical-top">
                        <p class="address text-center spacing-0 mt-4">
                            @if (isset($notes))
                                {!! $notes !!}
                                @php
                                    $length = mb_strlen(strip_tags($notes), 'UTF-8');
                                    $chunkSize = (int) round($chunkSize - $length / 112);
                                @endphp
                            @endif


                        </p>
                    </td>
                </tr>
            </table>
        </div>

        <div style="clear: both"></div>


        <div class="table_wrapper mt-24">
            <table class="table custom_datatable display" style="width: 100%" cellpadding="0" cellspacing="0">
                <thead>
                    <tr>
                        <th class="text-left" width="33%">Item / Description</th>
                        <th class="text-left">UoM</th>
                        <th class="text-left">Quantity</th>
                        <th class="text-left">Unit Cost</th>
                        <th class="text-left">Total Price</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($materialData->take($chunkSize) as $item)
                        <tr>
                            <td>{{ $item->cname }}</td>
                            <td>{{ $item->cuom }}</td>
                            <td>{{ $item->cquantity }}</td>
                            <td>${{ $item->ccost }}</td>
                            <td>${{ $item->ctotal_cost }}</td>
                        </tr>
                    @empty
                    @endforelse

                </tbody>
            </table>
        </div>


        @foreach ($materialData->slice($chunkSize)->chunk(14) as $chunk)
            <div style="clear: both"></div>
            <div class="table_wrapper mt-24" style="margin-top:100px">
                <table class="table custom_datatable display" style="width: 100%" cellpadding="0" cellspacing="0">
                    <thead>
                        <tr>
                            <th class="text-left" width="33%">Item / Description</th>
                            <th class="text-left">UoM</th>
                            <th class="text-left">Quantity</th>
                            <th class="text-left">Unit Cost</th>
                            <th class="text-left">Total Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($chunk as $item)
                            <tr>
                                <td>{{ $item->cname }}</td>
                                <td>{{ $item->cuom }}</td>
                                <td>{{ $item->cquantity }}</td>
                                <td>${{ $item->ccost }}</td>
                                <td>${{ $item->ctotal_cost }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endforeach


        <div style="page-break-inside: avoid;">
            <div class="total_payable w-100 text-right mt-24" style="margin-top:100px">
                <span class="title vertical-middle">Total Amount: </span>

                <span class="value vertical-middle"> $
                    {{ custom_number_format($generate_estimate?->grand_total) }}</span>
            </div>

            {{-- @php
                $clientSignature= new stdClass();
                $clientSignature->signature_text='Sign my djsf d fd f df sd';
                $clientSignature->signature_date=now();
            @endphp --}}
            <div class="approval_title w-100 mb-24 mt-16">Client Approval</div>
            <div class="approval_items w-100">
                <div class="sign float-left" style="width: 170px;">
                    <div class="signature signature_text mb-8"
                        style="{{ $clientSignature?->signature_text ? 'border-top:none' : '' }}">
                        {{ $clientSignature?->signature_text }} </div>
                    {{-- @if (!$clientSignature?->signature_text) --}}
                    <div class="sign_title">Signature</div>
                    {{-- @endif --}}
                </div>
                <div class="sign float-left" style="width: 110px">
                    <div class="signature mb-8 sign-date"
                        style="{{ $clientSignature?->signature_text ? 'border-top:none' : '' }}">
                        {{ $clientSignature?->signature_date ? customDateFormat(Carbon\Carbon::parse($clientSignature?->signature_date), $estimate->client->organization_id) : '' }}
                    </div>
                    {{-- @if (!$clientSignature?->signature_date) --}}
                    <div class="sign_title">Date</div>
                    {{-- @endif --}}
                </div>
            </div>
            <br />
            <div class="approval_title w-100 mb-24 mt-32">
                Company ( {{ $company->company_name }} ) Approval
            </div>
            <div class="approval_items w-100 mb-24" style="page-break-after: never;">
                <div class="sign float-left" style="width: 170px">
                    <div class="signature signature_text mb-8"></div>
                    <div class="sign_title">Signature</div>
                </div>
                <div class="sign float-left" style="width: 110px">
                    <div class="signature mb-8"></div>
                    <div class="sign_title">Date</div>
                </div>
            </div>
        </div> -->

    </div>
</body>

</html>
