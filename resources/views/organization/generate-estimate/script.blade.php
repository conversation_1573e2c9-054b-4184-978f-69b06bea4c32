<script type="text/javascript">
    var uom = '';
    var materialUom = '';
    var costUom = '';
    var costOtherCost = '';
    var equipment_cost = '';
    var equipment_uom = '';
    var equipment_margin = '';
    var unit_cost = '';
    var materialCost = '';
    var materialType = 'plant';
    var stepperIndex;
    var updateId = null;
    var laborId = null;
    var costId = null;
    var contractorId = null;
    var materialId = null;
    var setState = true;
    var setLaborState = true;
    var setMaterialState = true;
    var setCostState = true;

    // Setup Wizard Starts
    $(document).ready(function() {
        manipulateTable();
        var url = '{{ URL::route(getRouteAlias() . '.get-estimate-labor', ':id') }}',
            url = url.replace(':id', "{{ $estimate->id }}");
        getData(url, '#laborTable')
        var url = '{{ URL::route(getRouteAlias() . '.get-estimate-plant-material', ':id') }}',
            url = url.replace(':id', "{{ $estimate->id }}");
        getData(url, '#materialTable');
        var url = '{{ URL::route(getRouteAlias() . '.get-estimate-other-cost', ':id') }}',
            url = url.replace(':id', "{{ $estimate->id }}");
        getData(url, '#otherCostTable');
        var url =
            '{{ URL::route(getRouteAlias() . '.get-estimate-sub-contractor', ':id') }}',
            url = url.replace(':id', "{{ $estimate->id }}");
        getData(url, '#contractorTable');

        if ($("#smartwizard").length) {
            var wizard = $("#smartwizard").smartWizard({
                theme: "default", // theme for the wizard, related css need to include for other than default theme
                backButtonSupport: true, // Enable the back button support

                transition: {
                    animation: "fade", // Animation effect on navigation, none|fade|slideHorizontal|slideVertical|slideSwing|css(Animation CSS class also need to specify)
                    speed: "500", // Animation speed. Not used if animation is 'css'
                },
                toolbarSettings: {
                    toolbarPosition: "bottom", // none|top|bottom|both
                    showNextButton: true, // show/hide a Next button
                    showPreviousButton: true, // show/hide a Previous button
                },

                lang: {
                    // Language variables for button
                    next: "Next",
                    previous: "Back",
                },
            });

            wizard.on(
                "leaveStep",
                function(
                    e,
                    anchorObject,
                    currentStepIndex,
                    nextStepIndex,
                    stepDirection
                ) {
                    // If on the first step, add the disabled class to the back button
                    // console.log(currentStepIndex); // Check whether the `steps` property is defined
                    // console.log(wizard.steps.length);
                    stepperIndex = nextStepIndex;
                    if (stepDirection === "backward") {
                        if (currentStepIndex === 1) {
                            $(".wizard_back").addClass("disabled");
                        } else {
                            $(".wizard_back").removeClass("disabled");
                        }
                    } else {
                        $(".wizard_back").removeClass("disabled");
                    }

                    let stepslength = $(
                        ".setup_wizard_stepper .nav .nav-item"
                    ).length;

                    // If on the last step, change the next button text to "Generate"
                    if (nextStepIndex === stepslength - 1) {
                        $("#wizard_generate_btn").show();
                        $(".wizard_next").hide();
                    } else {
                        $("#wizard_generate_btn").hide();
                        $(".wizard_next").show();
                    }
                }
            );

            // Get the buttons and attach click event handlers to them
            var backButton = $(".wizard_back");
            var nextButton = $(".wizard_next");

            backButton.on("click", function() {
                if (!$(this).hasClass("disabled")) {
                    wizard.smartWizard("prev");
                }
            });

            nextButton.on("click", function() {
                if ($(this).hasClass("disabled")) {
                    return false;
                }
                wizard.smartWizard("next");
                if (stepperIndex) {
                    if (stepperIndex == 1) {
                        var url = '{{ URL::route(getRouteAlias() . '.get-estimate-labor', ':id') }}',
                            url = url.replace(':id', "{{ $estimate->id }}");
                        getData(url, '#laborTable');
                    }
                    if (stepperIndex == 2) {
                        var url =
                            '{{ URL::route(getRouteAlias() . '.get-estimate-plant-material', ':id') }}',
                            url = url.replace(':id', "{{ $estimate->id }}");
                        getData(url, '#materialTable');

                    }
                    if (stepperIndex == 3) {
                        var url =
                            '{{ URL::route(getRouteAlias() . '.get-estimate-other-cost', ':id') }}',
                            url = url.replace(':id', "{{ $estimate->id }}");
                        getData(url, '#otherCostTable');
                    }
                    if (stepperIndex == 4) {
                        var url =
                            '{{ URL::route(getRouteAlias() . '.get-estimate-sub-contractor', ':id') }}',
                            url = url.replace(':id', "{{ $estimate->id }}");
                        getData(url, '#contractorTable');

                    }

                }

            });


        }
    });
    // Setup Wizard Ends


    $(document).on('click', '#plantMaterial', function() {
        materialType = 'plant';
        $('#step3Form')[0].reset();
        var url = '{{ URL::route(getRouteAlias() . '.get-estimate-plant-material', ':id') }}',
            url = url.replace(':id', "{{ $estimate->id }}");
        getData(url, '#materialTable');

    })
    $(document).on('click', '#hardMaterial', function() {
        materialType = 'hard';
        $('#step3Form')[0].reset();
        var url = '{{ URL::route(getRouteAlias() . '.get-estimate-hard-material', ':id') }}',
            url = url.replace(':id', "{{ $estimate->id }}");
        getData(url, '#materialTable');
    })

    function getData(url, table) {

        $.ajax({
            url: url,
            type: 'GET',

            success: function(response) {
                $(table).html(JSON.parse(response))
            },
            error: function(response) {

            }
        })
    }

    function manipulateTable() {
        var url = '{{ URL::route(getRouteAlias() . '.get-estimate-equipment', ':id') }}',
            url = url.replace(':id', "{{ $estimate->id }}");
        $.ajax({
            url: url,
            type: 'GET',

            success: function(response) {
                $('#equipmentTable').html(JSON.parse(response))

            },
            error: function(response) {

            }
        })
    }

    function getval(object) {

        if (setState) {
            var opt = object.options[object.selectedIndex];
            $('#equipment-uom').val(opt.dataset.uom);
            $('#unitCost').val(opt.dataset.cost);
            $('#equipment-gross-margin').val(opt.dataset.margin);
            equipment_uom = opt.dataset.uom;
            equipment_margin = opt.dataset.margin;
            equipment_cost = opt.dataset.cost;
            $('.unitCost .invalid-feedback ').remove();
            $('.uom .invalid-feedback ').remove();
        }


    }
    $(document).on('click', '.selectEquipment', function() {
        setState = true;
    })
    $(document).on('click', '.selectLabor', function() {
        setLaborState = true;
    })
    $(document).on('click', '.selectMaterial', function() {
        setMaterialState = true;
    })

    $(document).on('click', '.selectCost', function() {
        setCostState = true;
    })

    function setServiceName(select) {


        // Get the selected option
        var selectedOption = select.options[select.selectedIndex];

        // Get the value of the data-service-name attribute of the selected option
        var serviceName = selectedOption.getAttribute("data-service-name");

        var serviceHtmlElement = document.getElementById("service-line-text");

        // Set the innerHTML of the HTML element
        serviceHtmlElement.innerHTML = serviceName;
        let formData = new FormData();
        formData.append('work_type_id', selectedOption.value);
        formData.append('request_id', "{{ request()->route()->parameter('id') }}");
        formData.append('service_line_id', selectedOption.getAttribute("data-service-id"));

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        var url = "{{ URL::route(getRouteAlias() . '.add-work-type') }}"
        $.ajax({
            method: "post",
            url: url,
            data: formData,
            cache: false,
            contentType: false,
            processData: false,


            success: function(response) {

            },
            error: function(response) {

            }
        })
    }


    function getLaborDetail(object) {
        if (setLaborState) {
            var opt = object.options[object.selectedIndex];
            $('#labor-uom').val(opt.dataset.uom);
            $('#labor-cost').val(opt.dataset.cost);
            $('#laborMargin').val(opt.dataset.margin);
            uom = opt.dataset.uom;
            unit_cost = opt.dataset.cost;
            $('.laborCost .invalid-feedback ').remove();
            $('.laborUom .invalid-feedback ').remove();
        }

    }

    function getMaterialDetail(object) {
        if (setMaterialState) {
            var opt = object.options[object.selectedIndex];
            $('#material-uom').val(opt.dataset.uom);
            $('#material-cost').val(opt.dataset.cost);
            $('#materialMargin').val(opt.dataset.margin);
            materialUom = opt.dataset.uom;
            materialCost = opt.dataset.cost;
        }
    }

    function getCostDetail(object) {
        console.log(object);
        if (setCostState) {
            var opt = object.options[object.selectedIndex];
            console.log(opt.dataset);
            $('#cost-uom').val(opt.dataset.uom);
            $('#cost-unit-cost').val(opt.dataset.cost);
            $('#cost-gross-margin').val(opt.dataset.margin);
            costUom = opt.dataset.uom;
            costOtherCost = opt.dataset.cost;
        }

    }


    $(document).on('click', '#addEquipment', function() {
        $('.invalid-feedback').html('');
        $('#addEquipment').prop('disabled', true);
        setTimeout(function() {
            $('#addEquipment').prop('disabled', false);
        }, 300);
        let form_data = $('#step1Form').serialize() +
            `&uom=${equipment_uom}&cost=${equipment_cost}&updateId=${updateId}`;

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        ajaxCAll(form_data);
    });

    $(document).on('click', '#addLabor', function() {
        $('.invalid-feedback').html('');
        $('#addLabor').prop('disabled', true);
        setTimeout(function() {
            $('#addLabor').prop('disabled', false);
        }, 300);
        let formData = $('#step2Form').serialize() +
            `&labor_uom=${uom}&labor_cost=${unit_cost}&laborId=${laborId}`;

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '{{ route(getRouteAlias() . '.estimate.add-labor') }}',
            type: 'POST',
            data: formData,

            success: function(response) {
                $('#laborTable').html(JSON.parse(response))
                $('#step2Form')[0].reset();
                laborId = null;


            },
            error: function(response) {
                $('.invalid-feedback').remove();

                $.each(response.responseJSON.errors, function(field_name, error) {
                    if (field_name == 'labor_id') {
                        $('#invalid-labor').html(`
                  <div class="invalid-feedback laravel_error" >  ${error[0]}  </div>

                  `);


                    } else {
                        $(document).find('[name=' + field_name + ']').after(
                            '<div class="invalid-feedback laravel_error" >' + error[0] +
                            '</div>');
                        $(".invalid-feedback").css("display", "block");
                    }

                });
            }
        })
    });

    $(document).on('click', '#addMaterial', function() {
        $('.invalid-feedback').html('');
        $('#addMaterial').prop('disabled', true);
        setTimeout(function() {
            $('#addMaterial').prop('disabled', false);
        }, 300);
        let formData = $('#step3Form').serialize() +
            `&material_uom=${materialUom}&material_cost=${materialCost}&type=${materialType}&materialId=${materialId}`;

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '{{ route(getRouteAlias() . '.estimate.add-material') }}',
            type: 'POST',
            data: formData,

            success: function(response) {
                $('#materialTable').html(JSON.parse(response))
                $('#step3Form')[0].reset();
                materialId = null;
                if (materialType == 'hard') {
                    // var url = '{{ URL::route(getRouteAlias() . '.get-estimate-hard-material', ':id') }}',
                    //     url = url.replace(':id', "{{ $estimate->id }}");
                    // getData(url, '#materialTable');
                }

            },
            error: function(response) {
                $('.invalid-feedback').remove();

                $.each(response.responseJSON.errors, function(field_name, error) {
                    if (field_name == 'hard_material_id' || field_name ==
                        'plant_material_id') {
                        if (field_name == "hard_material_id") {
                            $('#invalid-material_id').html(`
                  <div class="invalid-feedback laravel_error" >  ${error[0]}  </div>

                  `);
                        }
                        if (field_name == "plant_material_id") {
                            $('#invalid-plant_material_id').html(`
                  <div class="invalid-feedback laravel_error" >  ${error[0]}  </div>

                  `);
                        }

                    } else {
                        $(document).find('[name=' + field_name + ']').after(
                            '<div class="invalid-feedback laravel_error" >' + error[0] +
                            '</div>');
                        $(".invalid-feedback").css("display", "block");
                    }

                });
            }
        })
    });

    $(document).on('click', '#addOtherCost', function() {
        $('.invalid-feedback').html('');
        $('#addOtherCost').prop('disabled', true);
        setTimeout(function() {
            $('#addOtherCost').prop('disabled', false);
        }, 300);
        let formData = $('#step4Form').serialize() +
            `&cost_uom=${costUom}&cost_unit_cost=${costOtherCost}&costId=${costId}`;

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '{{ route(getRouteAlias() . '.estimate.add-other-cost') }}',
            type: 'POST',
            data: formData,

            success: function(response) {
                $('#otherCostTable').html(JSON.parse(response))
                $('#step4Form')[0].reset();



            },
            error: function(response) {
                $('.invalid-feedback').remove();

                $.each(response.responseJSON.errors, function(field_name, error) {
                    if (field_name == 'other_cost_id') {
                        $('#invalidOtherCost').html(`
                  <div class="invalid-feedback laravel_error" >  ${error[0]}  </div>

                  `);
                    } else {
                        $(document).find('[name=' + field_name + ']').after(
                            '<div class="invalid-feedback laravel_error" >' + error[0] +
                            '</div>');
                        $(".invalid-feedback").css("display", "block");
                    }

                });
            }
        })
    });
    $(document).on('click', '#addSubContractor', function() {
        $('.invalid-feedback').html('');
        $('#addSubContractor').prop('disabled', true);
        setTimeout(function() {
            $('#addSubContractor').prop('disabled', false);
        }, 300);
        let formData = $('#step5Form').serialize() + `&contractorId=${contractorId}`;
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '{{ route(getRouteAlias() . '.estimate.add-sub-contractor') }}',
            type: 'POST',
            data: formData,

            success: function(response) {
                $('#contractorTable').html(JSON.parse(response))
                $('#step5Form')[0].reset();
            },
            error: function(response) {

                $.each(response.responseJSON.errors, function(field_name, error) {

                    $(document).find('[name=' + field_name + ']').after(
                        '<div class="invalid-feedback laravel_error" >' + error[0] +
                        '</div>');
                    $(".invalid-feedback").css("display", "block");


                });
            }
        })
    });

    function editTableData(item) {
        equipment_cost = item.cost;
        equipment_uom = item.uom;
        updateId = item.id;
        setState = false;

        $('#equipment-uom').val(item.uom);
        $('#equipment-gross-margin').val(item.gross_margin);
        $('#unitCost').val(item.cost);
        $('#equipment-quantity').val(item.quantity);
        $('.selectEquipment').val(item.equipment_id);
        $('.selectEquipment').trigger('change');

    }

    function editLaborData(item) {
        unit_cost = item.unit_cost;
        uom = item.labor.uom;
        laborId = item.id;
        setLaborState = false;
        $('#labor-uom').val(item.labor.uom);
        $('#laborDescription').val(item.description);
        $('#laborMargin').val(item.gross_margin);
        $('#labor-cost').val(item.unit_cost);
        $('#laborQuantity').val(item.quantity);
        $('#laborId').val(item.labor_id);
        $("#laborId").trigger('change');

    }

    function editMaterialData(item) {
        materialCost = item.unit_cost;
        materialUom = item.uom;
        materialId = item.id;
        setMaterialState = false;

        $('#material-uom').val(item.uom);
        $('#materialMargin').val(item.gross_margin);
        $('#material-cost').val(item.unit_cost);
        $('#material-quantity').val(item.quantity);
        if (materialType == 'plant') {
            $('#selectedPlantMaterial').val(item.plant_material_id);
            $("#selectedPlantMaterial").trigger('change');

        }
        if (materialType == 'hard') {
            $('#selectedHardMaterial').val(item.hard_material_id);
            $("#selectedHardMaterial").trigger('change');
        }

    }

    function editCostData(item) {
        costOtherCost = item.unit_cost;
        costUom = item.uom;
        costId = item.id;
        setCostState = false;

        $('#cost-uom').val(item.uom);
        $('#other-cost-description').val(item.description);
        $('#cost-gross-margin').val(item.gross_margin);
        $('#cost-unit-cost').val(item.unit_cost);
        $('#other-cost-quantity').val(item.quantity);
        $('#other-cost-id').val(item.other_cost_id);
        $("#other-cost-id").trigger('change');
    }

    function editContractorData(item) {
        contractorId = item.id;
        $('#contractorUom').val(item.uom);
        $('#contractorName').val(item.name);
        $('#contractorMargin').val(item.gross_margin);
        $('#contractorCost').val(item.unit_cost);
        $('#contractorQuantity').val(item.quantity);
    }



    function ajaxCAll(formData) {

        $.ajax({
            url: '{{ route(getRouteAlias() . '.estimate.add-equipment') }}',
            type: 'POST',
            data: formData,


            success: function(response) {
                $('#equipmentTable').html(JSON.parse(response))
                $('#step1Form')[0].reset();
                $('#selectEquipmentId').prop('selectedIndex', 0);
                updateId = null;
            },
            error: function(response) {

                $.each(response.responseJSON.errors, function(field_name, error) {
                    if (field_name == 'equipment_id') {
                        $('#invalid-equipment').html(`
                  <div class="invalid-feedback laravel_error" >  ${error[0]}  </div>

                  `);
                        $(document).find('[name=' + 'unit_cost' + ']').after(
                            '<div class="invalid-feedback laravel_error" >' +
                            'This field is required' +
                            '</div>');
                        // $(document).find('[name=' + 'uom' + ']').after(
                        //     '<div class="invalid-feedback laravel_error" >' +
                        //     'This field is required' +
                        //     '</div>');

                    } else {
                        $(document).find('[name=' + field_name + ']').after(
                            '<div class="invalid-feedback laravel_error" >' + error[0] +
                            '</div>');
                        $(".invalid-feedback").css("display", "block");
                    }

                });
            }
        })

    }


    function deleteFromSession(id) {

        var url = '{{ URL::route(getRouteAlias() . '.estimate.equipment.delete', ':id') }}',
            url = url.replace(':id', id);
        $.ajax({
            url: url,
            type: 'GET',

            success: function(response) {
                manipulateTable();

            },
            error: function(response) {

            }
        })
    }

    function deleteLabor(id) {
        var url = '{{ URL::route(getRouteAlias() . '.estimate.labor.delete', ':id') }}',
            url = url.replace(':id', id);
        $.ajax({
            url: url,
            type: 'GET',

            success: function(response) {
                var url = '{{ URL::route(getRouteAlias() . '.get-estimate-labor', ':id') }}',
                    url = url.replace(':id', "{{ $estimate->id }}");
                getData(url, '#laborTable');

            },
            error: function(response) {

            }
        })
    }

    function deleteCost(id) {
        var url = '{{ URL::route(getRouteAlias() . '.estimate.other.cost.delete', ':id') }}',
            url = url.replace(':id', id);
        $.ajax({
            url: url,
            type: 'GET',

            success: function(response) {
                var url = '{{ URL::route(getRouteAlias() . '.get-estimate-other-cost', ':id') }}',
                    url = url.replace(':id', "{{ $estimate->id }}");
                getData(url, '#otherCostTable');

            },
            error: function(response) {

            }
        })
    }

    function deleteContractor(id) {
        var url = '{{ URL::route(getRouteAlias() . '.estimate.subcontractor.delete', ':id') }}',
            url = url.replace(':id', id);
        $.ajax({
            url: url,
            type: 'GET',

            success: function(response) {
                var url = '{{ URL::route(getRouteAlias() . '.get-estimate-sub-contractor', ':id') }}',
                    url = url.replace(':id', "{{ $estimate->id }}");
                getData(url, '#contractorTable');

            },
            error: function(response) {

            }
        })
    }

    function deleteMaterial(id) {
        var url = '{{ URL::route(getRouteAlias() . '.estimate.material.delete', ':id') }}',
            url = url.replace(':id', id);
        $.ajax({
            url: url,
            type: 'GET',
            data: {
                materialType
            },

            success: function(response) {
                if (materialType == 'plant') {
                    var url = '{{ URL::route(getRouteAlias() . '.get-estimate-plant-material', ':id') }}',
                        url = url.replace(':id', "{{ $estimate->id }}");
                } else if (materialType == 'hard') {
                    var url = '{{ URL::route(getRouteAlias() . '.get-estimate-hard-material', ':id') }}',
                        url = url.replace(':id', "{{ $estimate->id }}");
                }
                getData(url, '#materialTable');
            },
            error: function(response) {

            }
        })
    }
</script>
