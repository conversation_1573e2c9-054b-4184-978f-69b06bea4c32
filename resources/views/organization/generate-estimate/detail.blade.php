@extends('layouts.admin.master')
@section('title', 'Estimates')

@section('styles')
    <style>
        @media (min-width: 576px) {
            .modal-dialog {
                max-width: 500px !important;
            }
        }
    </style>
@endsection

@section('section')
    <section class="dashboard_main pb-5">
        <div class="panel mt-4">
            <div class="d-flex justify-content-between">
                <div class="panel_header">
                    <h2 class="panel_title">Request Details</h2>
                </div>
                <div class="d-flex align-items-center gap-3">
                    @if (optional($estimate->generateEstimate)?->is_archive == 1)
                        <a href="{{ route(getRouteAlias() . '.archive.unarchive', encodeId(optional($estimate->generateEstimate)?->id)) }}"
                            class="btn primaryblue">Unarchive</a>
                    @endif
                    <p class="text"><span class="placeholder-text">Request #: </span>{{ $estimate->job_no ?? '' }}</p>
                </div>
            </div>


            <div class="row gy-4 pt-4 pb-4 border-bottom">
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Client Name</label>
                        <p class="text">{{ $estimate->client?->full_name }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Company Name</label>
                        <p class="text">{{ $estimate->client?->company_name ?? '--- ---' }} </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Property</label>
                        <p class="text">{{ $estimate->propertyAddress?->property_name }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Address 1</label>
                        <p class="text">{{ $estimate->propertyAddress?->address1 }}</p>
                    </div>
                </div>
            </div>
            <div class="row gy-4 pt-4 pb-4 border-bottom">
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">City</label>
                        <p class="text">{{ $estimate->propertyAddress?->city }}</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">State</label>
                        <p class="text">{{ $estimate->propertyAddress?->state }}</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Zip Code</label>
                        <p class="text">{{ $estimate->propertyAddress?->zip }}</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Address 2</label>
                        <p class="text">{{ $estimate->propertyAddress?->address2 ?? '--- ---' }}</p>
                    </div>
                </div>
            </div>
            <div class="row gy-4 pt-4 pb-4 border-bottom">
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Phone Number</label>
                        <p class="text">{{ $estimate->client?->mobile_no }}</p>
                        @if ($estimate->client?->alternate_no)
                            <p class="text">{{ $estimate->client?->alternate_no }}</p>
                            <p class="placeholder-text font-14">(Alternate)</p>
                        @endif
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Email</label>
                        <p class="text">{{ $estimate->client?->email }}</p>
                        @if ($estimate->client?->alternate_email)
                            <p class="text">{{ $estimate->client?->alternate_email }}</p>
                            <p class="placeholder-text font-14">(Alternate)</p>
                        @endif
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Project Name</label>
                        <p class="text">{{ $estimate->project_name }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Lead Source</label>
                        <p class="text">{{ $estimate->lead_source ?? '--- ---' }}</p>
                    </div>
                </div>
            </div>
            <div class="row gy-4 pt-4 pb-4">
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Estimator Name</label>
                        <p class="text">{{ $estimate->estimator?->name }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Salesperson Name</label>
                        <p class="text">{{ $estimate->saleMan?->name }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Work Type</label>
                        <p class="text">{{ optional($estimate->generateEstimate)?->workType?->name ?: '---' }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Service Line</label>
                        <p class="text">{{ optional($estimate->generateEstimate)?->serviceLine?->name ?: '---' }}</p>
                    </div>
                </div>
            </div>

            <!-- Request Detail Info -->
        </div>
        <div class="panel mt-4">

            <div class="panel_header">
                <h2 class="panel_title">Summary</h2>

                <div class="scop-title-button">
                    @can('generate_estimate')
                        @if (previous_route()->getName() == getRouteAlias() . '.create.estimate-generate' && $finalPayableValue > 0)
                            <button class="scop-add-note-btn addDesireMargin_btn" id="margin-btn" style="width: 200px"
                                id="resetValues" type="button" data-bs-toggle="modal"
                                data-bs-target="#addGrossMarginModal">
                                @if (empty($desireMargin))
                                    Add Desire Margin
                                @else
                                    Update Desire Margin
                                @endif
                            </button>
                        @endif
                    @endcan
                </div>
            </div>

            <div class="row gy-4 p-4 mt-4 mb-5" style="border: 1px solid #E7E7E7; border-radius: 8px;">
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Equipment Cost:</label>
                        <p class="text">
                            ${{ custom_number_format(optional($estimateMaterial)?->sum('total_cost') ?? 0) }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Labor Cost:</label>
                        <p class="text">$ {{ custom_number_format($laborCost) }}
                        </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Labor Hours:</label>
                        <p class="text">
                            {{ custom_number_format($laborHours) }}
                        </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Supervision Hours:</label>
                        <p class="text">
                            {{ $superVisionHours }}
                        </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Material Cost:</label>

                        <p class="text">$
                            {{ custom_number_format($materialCost) }}
                        </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Other job Cost:</label>
                        <p class="text">$
                            {{ custom_number_format($otherJobCost) }}
                        </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Sub Contractor Cost:</label>
                        <p class="text">$
                            {{ custom_number_format($subContractorCost) }}
                        </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Gross Margin:</label>
                        <span class="text"
                            id="gross-margin">{{ $grossMarginPer ? custom_number_format($grossMarginPer) . '%' : '---' }}</span>

                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Desire Margin:</label>
                        <span
                            class="text ms-3 desire-margin-value">{{ $desireMargin ? custom_number_format($desireMargin) . '%' : '---' }}</span>

                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Total Price:</label>
                        <p class="text total-price"> {{ '$' . custom_number_format($finalPayableValue) }} </p>
                    </div>
                </div>
            </div>


            <div class="scop-work-title pb-2">
                <h2>Scope of Work</h2>

                <div class="scop-title-button addEditNote">
                    @include('organization.generate-estimate.partials.note-button', [
                        'generateEstimate' => $estimate->generateEstimate,
                    ]);
                </div>
            </div>
            <div class="general_accordian accordion mt-4">
                @if ($estimateMaterial->count() > 0)
                    <!-- Equipment Starts-->
                    <div class="accordion-item mt-4">
                        <h2 class="accordion-header" id="EquipmentData">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#EquipmentData-collapseOne" aria-expanded="true"
                                aria-controls="EquipmentData-collapseOne">
                                Equipment
                            </button>
                        </h2>
                        <div id="EquipmentData-collapseOne" class="accordion-collapse collapse show"
                            aria-labelledby="EquipmentData">
                            <div class="accordion-body">

                                <div class="table-responsive">
                                    <table class="table table-striped shadow-none custom_datatable display"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Items Name</th>
                                                <th>UoM</th>
                                                <th>Quantity</th>
                                                <th>Unit Cost</th>
                                                <th>Total Cost</th>
                                                <th>Gross Margin</th>
                                                <th>Unit price</th>
                                                <th>Total price</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($estimateMaterial as $item)
                                                <tr>
                                                    <td>{{ $item->equipment->name }}</td>
                                                    <td>{{ $item->uom ?? $item->equipment->uom }}</td>
                                                    <td>{{ $item->quantity }}</td>
                                                    <td>${{ custom_number_format($item->cost ?? $item->equipment->cost) }}
                                                    </td>
                                                    <td>${{ custom_number_format(($item->cost ?? $item->equipment->cost) * $item->quantity) }}
                                                    </td>
                                                    <td>{{ $item->gross_margin ?? 0 }}%</td>
                                                    <td>${{ custom_number_format($item->unit_price) }}
                                                    </td>
                                                    <td>${{ custom_number_format($item->total_price) }}
                                                    </td>

                                                </tr>
                                            @empty
                                            @endforelse

                                    </table>
                                </div>

                            </div>
                        </div>
                    </div>
                    <!-- Equipment Starts-->
                @endif

                @if ($estimateLabor->count() > 0)
                    <!-- Labor Starts-->
                    <div class="accordion-item mt-4">
                        <h2 class="accordion-header" id="LaborData">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#LaborData-collapseTwo" aria-expanded="true"
                                aria-controls="LaborData-collapseTwo">
                                Labor
                            </button>
                        </h2>
                        <div id="LaborData-collapseTwo" class="accordion-collapse collapse show"
                            aria-labelledby="LaborData">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table class="table table-striped shadow-none custom_datatable display"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Items Name</th>
                                                <th>Description</th>
                                                <th>UoM</th>
                                                <th>Quantity</th>
                                                <th>Unit Cost</th>
                                                <th>Total Cost</th>
                                                <th>Gross Margin</th>
                                                <th>Unit price</th>
                                                <th>Total price</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($estimateLabor as $item)
                                                <tr>
                                                    <td>{{ $item->labor?->name }}</td>
                                                    <td>{{ \Illuminate\Support\Str::limit($item->description, 50, $end = '...') }}
                                                    </td>
                                                    <td>{{ $item->labor?->uom }}</td>
                                                    <td>{{ $item->quantity }}</td>
                                                    <td>${{ custom_number_format($item->unit_cost) }}</td>
                                                    <td>${{ custom_number_format($item->total_cost) }}</td>
                                                    <td>{{ $item->gross_margin }}%</td>
                                                    <td>${{ custom_number_format($item->grand_total / $item->quantity) }}
                                                    </td>
                                                    <td>${{ custom_number_format($item->grand_total) }} </td>

                                                </tr>
                                            @empty
                                            @endforelse
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Labor Starts-->
                @endif


                @if ($estimatePlantMaterial->count() > 0)
                    <!-- Plant Materials Starts-->
                    <div class="accordion-item mt-4">
                        <h2 class="accordion-header" id="PlantMaterialsData">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#PlantMaterialsData-collapseThree" aria-expanded="true"
                                aria-controls="PlantMaterialsData-collapseThree">
                                Plant Materials
                            </button>
                        </h2>
                        <div id="PlantMaterialsData-collapseThree" class="accordion-collapse  collapse show"
                            aria-labelledby="PlantMaterialsData">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table class="table table-striped shadow-none custom_datatable display"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Items Name</th>
                                                <th>UoM</th>
                                                <th>Quantity</th>
                                                <th>Unit Cost</th>
                                                <th>Total Cost</th>
                                                <th>Gross Margin</th>
                                                <th>Unit price</th>
                                                <th>Total price</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($estimatePlantMaterial as $item)
                                                <tr>
                                                    <td>{{ $item->material?->name }}</td>
                                                    <td>{{ $item->uom }}</td>
                                                    <td>{{ $item->quantity }}</td>
                                                    <td>${{ custom_number_format($item->unit_cost) }}</td>
                                                    <td>${{ custom_number_format($item->total_cost) }}
                                                    </td>
                                                    <td>{{ $item->gross_margin }}%</td>
                                                    <td>${{ custom_number_format($item->unit_price) }}
                                                    </td>
                                                    <td>${{ custom_number_format($item->total_price) }}
                                                    </td>
                                                </tr>
                                            @empty
                                            @endforelse
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Plant Materials Starts-->
                @endif

                @if ($estimateHardMaterial->count() > 0)
                    <!-- Hard Materials Starts-->
                    <div class="accordion-item mt-4">
                        <h2 class="accordion-header" id="HardMaterialsData">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#HardMaterialsData-collapseThree" aria-expanded="true"
                                aria-controls="HardMaterialsData-collapseThree">
                                Hard Materials
                            </button>
                        </h2>
                        <div id="HardMaterialsData-collapseThree" class="accordion-collapse  collapse show"
                            aria-labelledby="HardMaterialsData">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table class="table table-striped shadow-none custom_datatable display"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Items Name</th>
                                                <th>UoM</th>
                                                <th>Quantity</th>
                                                <th>Unit Cost</th>
                                                <th>Total Cost</th>
                                                <th>Gross Margin</th>
                                                <th>Unit price</th>
                                                <th>Total price</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($estimateHardMaterial as $item)
                                                <tr>
                                                    <td>{{ $item->material?->name }}</td>
                                                    <td>{{ $item->uom }}</td>
                                                    <td>{{ $item->quantity }}</td>
                                                    <td>${{ custom_number_format($item->unit_cost) }}</td>
                                                    {{-- <td>${{ custom_number_format($item->quantity * $item->unit_cost) }}
                                                    </td> --}}
                                                    <td>{{ custom_number_format($item->total_cost) }}</td>
                                                    <td>{{ $item->gross_margin }}%</td>
                                                    <td>${{ custom_number_format($item->unit_price) }}
                                                    </td>
                                                    <td>${{ custom_number_format($item->total_price) }}
                                                    </td>
                                                </tr>
                                            @empty
                                            @endforelse
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Hard Materials Starts-->
                @endif

                @if ($estimateOtherCost->count() > 0)
                    <!-- Other job Cost Starts-->
                    <div class="accordion-item mt-4">
                        <h2 class="accordion-header" id="OtherjobCostData">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#OtherjobCostData-collapseThree" aria-expanded="true"
                                aria-controls="OtherjobCostData-collapseThree">
                                Other job Cost
                            </button>
                        </h2>
                        <div id="OtherjobCostData-collapseThree" class="accordion-collapse  collapse show"
                            aria-labelledby="OtherjobCostData">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table class="table table-striped shadow-none custom_datatable display"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Items Name</th>
                                                <th>Description</th>
                                                <th>UoM</th>
                                                <th>Quantity</th>
                                                <th>Unit Cost</th>
                                                <th>Total Cost</th>
                                                <th>Gross Margin</th>
                                                <th>Unit price</th>
                                                <th>Total price</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($estimateOtherCost as $item)
                                                <tr>
                                                    <td>{{ $item->otherCost?->name }}</td>
                                                    <td>{{ \Illuminate\Support\Str::limit($item->description, 50, $end = '...') }}
                                                    </td>
                                                    <td>{{ $item->uom }}</td>
                                                    <td>{{ $item->quantity }}</td>
                                                    <td>${{ custom_number_format($item->unit_cost) }}</td>
                                                    <td>${{ custom_number_format($item->unit_cost * $item->quantity) }}
                                                    </td>
                                                    <td>{{ $item->gross_margin ?? 0 }}%</td>
                                                    <td>${{ custom_number_format($item->unit_price) }}
                                                    </td>
                                                    <td>${{ custom_number_format($item->total_price) }}
                                                    </td>
                                                </tr>
                                            @empty
                                            @endforelse

                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Other job Cost Starts-->
                @endif

                @if ($estimateSubContractor->count() > 0)
                    <!-- Sub-Contractor Starts-->
                    <div class="accordion-item mt-4">
                        <h2 class="accordion-header" id="SubContractorData">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#SubContractorData-collapseThree" aria-expanded="true"
                                aria-controls="SubContractorData-collapseThree">
                                Sub-Contractor
                            </button>
                        </h2>
                        <div id="SubContractorData-collapseThree" class="accordion-collapse collapse show"
                            aria-labelledby="SubContractorData">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table class="table table-striped shadow-none custom_datatable display"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Description/Items</th>
                                                <th>UoM</th>
                                                <th>Quantity</th>
                                                <th>Unit Cost</th>
                                                <th>Total Cost</th>
                                                <th>Gross Margin</th>
                                                <th>Unit price</th>
                                                <th>Total price</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($estimateSubContractor as $item)
                                                <tr>
                                                    <td>{{ $item->name }}</td>
                                                    <td>{{ $item->uom }}</td>
                                                    <td>{{ $item->quantity }}</td>
                                                    <td>${{ custom_number_format($item->unit_cost) }}</td>
                                                    <td>${{ custom_number_format($item->quantity * $item->unit_cost) }}
                                                    </td>
                                                    <td>{{ $item->gross_margin }}%</td>
                                                    <td>${{ custom_number_format($item->unit_price) }}
                                                    </td>
                                                    <td>${{ custom_number_format($item->total_price) }}

                                                </tr>
                                            @empty
                                            @endforelse
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Sub-Contractor Starts-->
                @endif

                {{-- <div>
                <p>Testing Details</p>
                <p>Material Cost: {{ $materialCost }}</p>
                <p>Sales Tax%: {{ getSaleTax() }}</p>
                <p>Labor Cost: {{ $laborCost }}</p>
                <p>Sales Tax final: {{ ($materialCost * getSaleTax()) / 100 }}</p>
                <p>Total Cost: {{ $totalCost }}</p>
                <p>Total Price: </p>
                <p>Finak Total Cost: {{ $totalCost + $laborCost + ($materialCost * getSaleTax()) / 100 }}</p>
                <p>Margin: </p>
                <p>Desired Margin: </p>
                <p>Final Selling Price: </p>
            </div> --}}
                <div class="total_payable d-flex justify-content-end align-items-center gap-4 flex-wrap mt-4 mb-4">
                    <p class="text">Total Price:</p>
                    <h2 class="value totalPayableValue">$ {{ custom_number_format($finalPayableValue) }} </h2>
                </div>
            </div>

            @can('generate_estimate')
                <div class="d-flex align-items-center justify-content-between gap-4 flex-wrap mt-5">
                    <a href="{{ route(getRouteAlias() . '.estimate-generate.index') }}"
                        class="btn primaryblue transparent min-w-174" type="button">Cancel</a>

                    @if ($finalPayableValue > 0)
                        @if (previous_route()->getName() == getRouteAlias() . '.create.estimate-generate')
                            <div class="d-flex align-items-center justify-content-between gap-4 flex-wrap">
                                <a href="{{ route(getRouteAlias() . '.estimate-generate.store', ['id' => encodeId($estimate->id)]) }}"
                                    class="btn primaryblue transparent min-w-174" type="button">Save</a>
                                <a data-url="{{ route(getRouteAlias() . '.estimate-generate.download', ['id' => encodeId($estimate->id)]) }}"
                                    href="{{ route(getRouteAlias() . '.estimate-generate.download', ['id' => encodeId($estimate->id)]) }}"
                                    data-file-name="{{ 'estimate_' . (($estimate?->sales_order_number ?: 0) == 0 ? $estimate?->job_no : 'invoice') }}"
                                    class="btn primaryblue min-w-174 saveAndDownload send-email" type="button">Save &
                                    Download</a>
                            </div>
                        @else
                            <div class="d-flex align-items-center justify-content-between gap-4 flex-wrap">
                                <a data-url="{{ route(getRouteAlias() . '.estimate-generate.only-download', ['id' => encodeId($estimate->id)]) }}"
                                    href="{{ route(getRouteAlias() . '.estimate-generate.only-download', ['id' => encodeId($estimate->id)]) }}"
                                    data-file-name="{{ 'estimate_' . (($estimate?->sales_order_number ?: 0) == 0 ? $estimate?->job_no : 'invoice') }}"
                                    class="btn primaryblue min-w-174 downloadPDF" type="button">Download</a>
                            </div>
                        @endif
                    @endif
                </div>

            @endcan
    </section>

    @include('layouts.partials.success-modal')
    <!-- Modal -->

    <span id="notePartial">
        @include('organization.generate-estimate.partials.note', [
            'generateEstimate' => $estimate->generateEstimate,
        ])
    </span>

    <form id="addDesireMargin" method='' action="">
        @csrf
        <div class="operation_modal modal fade" id="addGrossMarginModal" data-bs-keyboard="false" tabindex="-1"
            aria-labelledby="editNoteModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-md" style="max-width: 465px !important">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title desireMargin-Label" id="editNoteModalLabel">
                            @if (empty($desireMargin))
                                Add Desire Margin (%)
                            @else
                                Update Desire Margin (%)
                            @endif
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body pb-0">
                        <input type="number" name="desireMargin" class="invoice-1-content desire-margin"
                            style="width: 100%" min="1" max="99" placeholder="%" required
                            value="{{ $desireMargin }}">
                    </div>
                    <div id="invalid-note"></div>

                    <div class="modal-footer gap-4 mb-3">

                        <button type="button" class="cancelModal invoice-pg-2-cls"
                            data-bs-dismiss="modal">Cancel</button>
                        <button type="button" date-value="updated"
                            class=" AddDesireMarginBtn invoice-pg-2-sv">Add</button>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- Modal -->
    @include('organization.partials.send-email', [
        'to_email' => $estimate->client->email,
        'sentInvoiceAttachment' => false,
        'isExternalSubmitHandler' => 'sendEmailHandler',
        'isOperational' => true,
        'sales_order_number' => $estimate?->job_no,
    ])


    @push('scripts')
        <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
        @include('organization.generate-estimate.detail-script')
    @endpush
@endsection
