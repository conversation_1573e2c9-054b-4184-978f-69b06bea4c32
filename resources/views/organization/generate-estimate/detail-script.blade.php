<script type="text/javascript">
    $(document).on('click', '#saveNotes', function() {
        $('#invalid-note').html(``);
        var editorContent = $('.addNoteSummer').summernote('code');
        var strippedContent = editorContent.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, '').trim();
        var isEmpty = strippedContent.length === 0;
        if (isEmpty) {
            $('#invalid-note').html(`
    <div class="invalid-feedback laravel_error" style="display:block;margin-left:2rem" >  This field is required.  </div>
  `);
            return;
        }
        if ($('.addNoteSummer').summernote('isEmpty')) {
            $('#invalid-note').html(`
                  <div class="invalid-feedback laravel_error" style="display:block;margin-left:2rem" >  This field is required.  </div>

                  `);
            return;
        }

        var successValue = $(this).attr('date-value');
        var url = '{{ URL::route(getRouteAlias() . '.add-note', ':id') }}',
            url = url.replace(':id', "{{ $estimate->id }}");
        let form_data = $('#addEstimateNote').serialize()
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });


        $.ajax({
            url: url,
            type: 'POST',
            data: form_data,

            success: function(response) {
                // $('#successModal').modal({
                //     backdrop: 'static',
                //     keyboard: false
                // })
                toastr.success(response.success);
                $('#addNoteModal').modal('hide');
                $('#editNoteModal').modal('hide');
                $('#addEstimateNote')[0].reset();
                $("#notePartial").html(response.html);
                if ($(".addNoteSummer").length) {
                    $(".addNoteSummer").summernote({
                        placeholder: "Enter description",
                        tabsize: 2,
                        height: 380,
                        toolbar: [
                            ["style", ["italic", "bold", "underline", "strikethrough"]],
                            ["alignment", ["ul", "paragraph", "left", "center",
                                "right"
                            ]],
                        ],
                    });
                }
                $(".addEditNote").html(response.buttons);
                // $('#successModal').modal('show');
                // $('.dynamic-success-data').html('');
                // $('.dynamic-success-data').append(
                //     ` <h2 class="title text-center">Done</h2>
                //  <p class="para mt-3 text-center">Notes ${successValue} successfully!</p>`
                // );
            },
            error: function(response) {
                $.each(response.responseJSON.errors, function(field_name, error) {
                    if (field_name == 'description') {
                        $('#invalid-note').html(`
                  <div class="invalid-feedback laravel_error" style="display:block;margin-left:2rem" >  ${error[0]}  </div>

                  `);
                    }
                });
            }
        })
    })
    $(document).on('click', '.deleteNote', function() {
        var url = '{{ URL::route(getRouteAlias() . '.delete-note', ':id') }}',
            url = url.replace(':id', "{{ $estimate->id }}");
        $.ajax({
            method: "DELETE",
            url: url,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },

            success: function(response) {
                // $('#successModal').modal({
                //     backdrop: 'static',
                //     keyboard: false
                // })
                toastr.success(response.success);
                // $('#successModal').modal('show');
                $("#notePartial").html(response.html);
                if ($(".addNoteSummer").length) {
                    $(".addNoteSummer").summernote({
                        placeholder: "Enter description",
                        tabsize: 2,
                        height: 380,
                        toolbar: [
                            ["style", ["italic", "bold", "underline", "strikethrough"]],
                            ["alignment", ["ul", "paragraph", "left", "center",
                                "right"
                            ]],
                        ],
                    });
                }
                $(".addEditNote").html(response.buttons);
                // $('.dynamic-success-data').html('');
                // $('.dynamic-success-data').append(
                //     ` <h2 class="title text-center">Done</h2>
                //  <p class="para mt-3 text-center">Notes Deleted Successfully!</p>`
                // );
            },
            error: function(response) {}
        })
    })
    $(document).on('click', '#continue', function() {
        window.location.reload();
    })
    $(document).on('click', '.cancel', function() {
        window.location.reload();
    })
    $(document).on('click', '.scop-add-note-btn', function() {
        $('#invalid-note').html(``);
    })
    $(document).on('click', '#resetValues', function() {
        $('.addNoteSummer').summernote('code', '');
    })

    $(document).on('click', '#successCloseModal', function() {
        $('#successModal').modal('hide');
    })

    $(document).on('keyup', '.note-editable', function() {
        $('#invalid-note').html(``);
    })

    $(document).on('click', '.AddDesireMarginBtn', function() {

        let inputField = $('input[name="desireMargin"]');
        let inputValue = inputField.val();

        // Perform input validation
        if (inputValue < 1 || inputValue >= 100) {
            // Invalid value, show appropriate error message or handle validation error
            $('#addDesireMargin #invalid-note').html(`
                  <div class="invalid-feedback laravel_error" style="display:block;margin-left:2rem" >Value must be between 1 and 99. </div>
                  `);
            return;
        }
        var url = '{{ URL::route(getRouteAlias() . '.add-desire-margin', ':id') }}',
            url = url.replace(':id', "{{ $estimate->id }}");
        let form_data = $('#addDesireMargin').serialize()
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        $.ajax({
            url: url,
            type: 'POST',
            data: form_data,

            success: function(response) {
                $('#successModal').modal({
                    backdrop: 'static',
                    keyboard: false
                })
                $('.laravel_error').remove();
                $('#addGrossMarginModal').modal('hide');
                $('#addDesireMargin')[0].reset();
                $('#successModal').modal('show');
                $('.dynamic-success-data').html('');
                $('.dynamic-success-data').append(
                    ` <h2 class="title text-center">Done</h2>
                 <p class="para mt-3 text-center">Desire Margin added  successfully!</p>`
                );

                $('.dynamic-success-data + button').attr('id', 'successCloseModal');
                $(".desire-margin-value").text(response.desire_margin + '%');
                $('#addDesireMargin .desire-margin').val(response.desire_margin);
                $('#addDesireMargin .desireMargin-Label').text('Update Desire Margin (%)');
                $('.addDesireMargin_btn').text('Update Desire Margin');
                var totalPrice = document.getElementsByClassName('total-price');

                if (totalPrice.length > 0) {
                    var desireMargin = response.desire_margin;
                    var desiredMarginAmount = parseFloat(response.grand_total);
                    totalPrice[0].textContent = '$' + desiredMarginAmount.toFixed(2);

                    var totalPayableElement = document.getElementsByClassName('totalPayableValue');
                    if (totalPayableElement.length > 0) {
                        totalPayableElement[0].textContent = '$' + desiredMarginAmount.toFixed(2)
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                    }
                }

            },
            error: function(response) {
                $.each(response.responseJSON.errors, function(field_name, error) {
                    if (field_name == 'description') {
                        $('#invalid-note').html(`
                  <div class="invalid-feedback laravel_error" style="display:block;margin-left:2rem" >  ${error[0]}  </div>

                  `);
                    }
                });
            }
        })
    })
</script>
<script>
    $(document).ready(function() {
        $(".saveAndDownload").click(function(e) {
            e.preventDefault();
            if ($(e.target).hasClass('send-email')) {
                // Set Email Modal Header
                $('#emailModal .modal-title').html(
                    "Email Estimate #{{ optional($estimate->request)?->sales_order_number }} to " +
                    "{{ $estimate->client->full_name }}")

                // Set Email Defualt Subject

                $('#emailModal #subject').val(
                    'Estimate From {{ optional($organization)?->company_name }} - {{ customDateFormat(now()) }}'
                )

                var website = @json(optional($organization->companyAddress)->website_url);
                var sumhtml = '<span class="im">' +
                    '<h1 style="font-style: normal; font-weight: 700; font-size: 24px; line-height: 33.6px; color: rgb(25, 42, 62);">Hi {{ $estimate->client->full_name }}, </h1>' +
                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 24px;">Thank you for choosing {{ $organization->company_name }}. </p>' +
                    '<p style="font-family: Arial, Helvetica, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 24px;">The estimate total is <strong style="color: rgb(25, 42, 62);">' +
                    $('.total-price').text() +
                    '</strong></p>' +
                    '<p><span class="im" style="color: rgb(80, 0, 80); font-family: Arial, Helvetica, sans-serif; font-size: small;"></span><span class="im" style="color: rgb(80, 0, 80); font-family: Arial, Helvetica, sans-serif; font-size: small;"></span></p>' +
                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 10px;">If you have any questions, please do not hesitate to contact us at<span>&nbsp;</span><a target="_blank" rel="noopener noreferrer" href="mailto:{{ optional($organization)->email }}" style="color: rgb(17, 85, 204);"> {{ optional($organization)->email }}</a>.<br>' +
                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 10px;"><br>Regards, </p></br>' +
                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ $organization->company_name }} </strong> </p>' +
                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ $organization->email }} </strong> </p>' +
                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ optional($organization->companyAddress)->phone_no }} </strong> </p>' +
                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ optional($organization->companyAddress)->address1 }} </strong> </p>';

                if (website) {
                    sumhtml +=
                        '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> ' +
                        website + ' </strong> </p>';
                }
                $('#emailModal .summernote').summernote('code', sumhtml);
                // Set Email Defualt Message
                $('#emailModal').modal('show')

            }

        });

        $(".downloadPDF").click(function(e) {
            e.preventDefault();
            var downloadUrl = e.target.getAttribute('data-url');
            var fileName = e.target.getAttribute('data-file-name');
            $.ajax({
                type: 'GET',
                url: downloadUrl,
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(response) {
                    // Check if the response is not empty
                    if (response.size > 0) {
                        // File download success, use FileSaver.js to trigger the download

                        saveAs(response, fileName + '.pdf');
                    } else {
                        toastr.error("File not found!");
                    }
                },
                error: function(xhr, status, error) {
                    console.error(error);
                    toastr.error("Error occurred while downloading the file.");
                }
            });
        });
    });
</script>

<script>
    $(document).on("sendEmailHandler", sendEmailHandler);

    function sendEmailHandler(e) {
        var formData = {};
        if (e.customData instanceof FormData) {
            formData = e.customData;
        }

        formData.append('sentEmail', true);

        var downloadUrl = $('.saveAndDownload').attr('data-url');
        var fileName = $('.saveAndDownload').attr('data-file-name');

        $.ajax({
            url: downloadUrl,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhrFields: {
                responseType: 'blob'
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                // Check if the response is not empty
                if (response.size > 0) {
                    // File download success, use FileSaver.js to trigger the download
                    saveAs(response, fileName + '.pdf');

                    toastr.success("Estimate save successfully!");
                    window.location.href =
                        "{{ route(getRouteAlias() . '.estimate-generate.index') }}";
                } else {
                    toastr.error("File not found!");
                }

            },
            error: function(xhr, status, error) {
                console.error(error);
                toastr.error("Error occurred while downloading the file.");
            }
        });
    }
</script>
