<div class="table-responsive">
    <table class="table table-striped custom_datatable display" style="width:100%">
        <thead>
            <tr>
                <th>Description</th>
                <th>UOM</th>
                <th>Quantity</th>
                <th>Unit cost</th>
                <th>Total cost</th>
                <th>Gross Margin</th>
                <th>Unit Price</th>
                <th>Total Price</th>
                <th class="text-center">Action</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($estimateSubContractor as $item)
                <tr>
                    <td>{{ $item->name }}</td>
                    <td>{{ $item->uom }}</td>
                    <td>{{ $item->quantity }}</td>
                    <td>${{ custom_number_format( $item->unit_cost ) }}</td>
                    <td>${{ custom_number_format( $item->quantity * $item->unit_cost ) }}</td>
                    <td>{{ $item->gross_margin }}%</td>
                    <td>${{ custom_number_format( $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ) }}</td>
                    <td>${{ custom_number_format( $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity) ) }}
                    <td>
                        <div class="d-flex align-items-center gap-4">
                            <a onclick="editContractorData({{ $item }})"><i
                                    class="fa-solid fa-pencil pencil_icon"></i></a>
                            <a onclick="deleteContractor({{ $item->id }})"><i
                                    class="fa-regular fa-trash-can text-danger trash_icon"></i></a>

                        </div>
                    </td>
                </tr>
            @empty
            @endforelse

    </table>
</div>
