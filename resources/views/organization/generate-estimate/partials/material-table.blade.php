<div class="panel mt-4">
    <h3 class="h3">Material</h3>
    <hr class="my-4">

    <div class="table-responsive">
        <table class="table table-striped custom_datatable display" style="width:100%">
            <thead>
                <tr>
                    <th>Items Name</th>
                    <th>UoM</th>
                    <th>Quantity</th>
                    <th>Unit cost</th>
                    <th>Total cost</th>
                    <th>Gross Margin</th>
                    <th>Unit Price</th>
                    <th>Total Price</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>

                @forelse ($estimateMaterial as $item)
                    <tr>
                        <td>{{ $item->material?->name }}</td>
                        <td>{{ $item->uom }}</td>
                        <td>{{ $item->quantity }}</td>
                        <td>${{ custom_number_format($item->unit_cost) }}</td>
                        <td>${{ custom_number_format($item->total_cost) }}</td>
                        <td>{{ $item->gross_margin }}%</td>
                        <td>${{ custom_number_format(totalPriceWithTax($item->quantity, $item->unit_cost, $item->gross_margin) / $item->quantity) }}
                        </td>
                        <td>${{ custom_number_format(totalPriceWithGrossMargin($item->total_cost, $item->gross_margin)) }}
                        </td>

                        <td>
                            <div class="d-flex align-items-center gap-4">
                                <a onclick="editMaterialData({{ $item }})"><i
                                        class="fa-solid fa-pencil pencil_icon"></i></a>
                                <a onclick="deleteMaterial({{ $item->id }})"><i
                                        class="fa-regular fa-trash-can text-danger trash_icon"></i></a>

                            </div>
                        </td>
                    </tr>
                @empty
                @endforelse




        </table>
    </div>
</div>
