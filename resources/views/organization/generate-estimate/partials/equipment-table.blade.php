<div class="panel mt-4">
    <h3 class="h3">Equipment</h3>
    <hr class="my-4">

    <div class="table-responsive">
        <table class="table table-striped custom_datatable display" style="width:100%">
            <thead>
                <tr>
                    <th>Items Name</th>
                    <th>UoM</th>
                    <th>Quantity</th>
                    <th>Unit Cost</th>
                    <th>Total Cost</th>
                    <th>Gross Margin</th>
                    <th>Unit Price</th>
                    <th>Total Price</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @forelse($estimateMaterial as $item)
                    <tr>
                        <td>{{ $item->name ?? $item->equipment->name }}</td>
                        <td>{{ $item->uom ?? $item->equipment->uom }}</td>
                        <td>{{ $item->quantity }}</td>
                        <td>${{ custom_number_format($item->cost ?? $item->equipment->cost) }}</td>
                        <td>${{ custom_number_format(($item->cost ?? $item->equipment->cost) * $item->quantity) }}</td>
                        <td>{{ $item->gross_margin ?? '' }}%</td>
                        <td>${{ custom_number_format(($item->cost ?? $item->equipment->cost) + ($item->gross_margin / 100) * ($item->cost ?? $item->equipment->cost)) }}
                        </td>
                        <td>${{ custom_number_format($item->total_cost + ($item->gross_margin / 100) * $item->total_cost) }}</td>


                        <td>
                            <div class="d-flex align-items-center justify-content-center gap-4">
                                <a onclick="editTableData({{ $item }})"><i
                                        class="fa-solid fa-pencil pencil_icon"></i></a>
                                <a onclick="deleteFromSession({{ $item->id }})"><i
                                        class="fa-regular fa-trash-can text-danger trash_icon"></i></a>
                            </div>
                        </td>
                    </tr>
                @empty
                @endforelse


        </table>
    </div>
</div>
