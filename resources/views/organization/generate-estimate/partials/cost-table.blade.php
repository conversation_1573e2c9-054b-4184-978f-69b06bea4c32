<div class="panel mt-4">
    <h3 class="h3">Other Job Cost</h3>
    <hr class="my-4">

    <div class="table-responsive">
        <table class="table table-striped custom_datatable display" style="width:100%">
            <thead>
                <tr>
                    <th>Item Name</th>
                    <th>Description</th>
                    <th>UoM</th>
                    <th>Quantity</th>
                    <th>Unit Cost</th>
                    <th>Total Cost</th>
                    <th>Gross Margin</th>
                    <th>Unit Price</th>
                    <th>Total Price</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($estimateOtherCost as $item)
                    <tr>
                        <td>{{ $item->otherCost?->name }}</td>
                        <td>{{ $item->description }}</td>
                        <td>{{ $item->uom }}</td>
                        <td>{{ $item->quantity }}</td>
                        <td>${{ custom_number_format($item->unit_cost) }}</td>
                        <td>${{ custom_number_format($item->unit_cost * $item->quantity) }}</td>
                        <td>{{ $item->gross_margin }}%</td>
                        <td>${{ custom_number_format($item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost) }}
                        </td>
                        <td>${{ custom_number_format($item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity)) }}
                        </td>

                        <td>
                            <div class="d-flex align-items-center gap-4">
                                <a onclick="editCostData({{ $item }})"><i
                                        class="fa-solid fa-pencil pencil_icon"></i></a>
                                <a onclick="deleteCost({{ $item->id }})"><i
                                        class="fa-regular fa-trash-can text-danger trash_icon"></i></a>

                            </div>
                        </td>
                    </tr>
                @empty
                @endforelse


        </table>
    </div>
</div>
