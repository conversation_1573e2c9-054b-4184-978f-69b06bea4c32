<div class="panel mt-4">
    <h3 class="h3">Labor</h3>
    <hr class="my-4">

    <div class="table-responsive">
        <table class="table table-striped custom_datatable display" style="width:100%">
            <thead>
                <tr>
                    <th>Items Name</th>
                    <th>Description</th>
                    <th>UoM</th>
                    <th>Quantity</th>
                    <th>Unit cost</th>
                    <th>Total cost</th>
                    <th>Gross Margin</th>
                    <th> Unit Price</th>
                    <th>Total Price</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>

                @forelse($estimateLabor as $item)
                    <tr>
                        <td>{{ $item->labor?->name }}</td>
                        <td>{{ \Illuminate\Support\Str::limit($item->description, 50, $end = '...') }}</td>
                        <td>{{ $item->labor?->uom }}</td>
                        <td>{{ $item->quantity }}</td>
                        <td>${{ custom_number_format($item->unit_cost) }}</td>
                        <td>${{ custom_number_format($item->total_cost) }}</td>
                        <td>{{ $item->gross_margin }}%</td>
                        <td>${{ custom_number_format($item->grand_total / $item->quantity) }}</td>
                        <td>${{ custom_number_format($item->grand_total) }}</td>

                        {{--
                            total_cost = 120 ;
                            laborBurdon = 20%;
                            groos_margin=70%;
                            laborRate=total_cost+laborBurden - total_cost;
                            total=total_cost + gross_margin + laborRate
                         --}}



                        <td>
                            <div class="d-flex align-items-center gap-4">
                                <a onclick="editLaborData({{ $item }})"><i
                                        class="fa-solid fa-pencil pencil_icon"></i></a>
                                <a onclick="deleteLabor({{ $item->id }})"><i
                                        class="fa-regular fa-trash-can text-danger trash_icon"></i></a>

                            </div>
                        </td>
                    </tr>
                @empty
                @endforelse


        </table>
    </div>
</div>
