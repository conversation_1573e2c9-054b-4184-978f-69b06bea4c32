@if ($generateEstimate?->notes)
<div class="operation_modal modal fade" id="viewNoteModal" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="viewNoteModalLabel-1" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="staticBackdropLabel-1">View Note</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="invoice-1-content">
                    {!! nl2br(str_replace('<strike>', '<strike class="strikethrough">', $generateEstimate->notes)) !!}

                </div>
            </div>
            <div class="modal-footer gap-4">
                {{-- <button type="button" class=" invoice-pg-2-dl">Delete</button> --}}
                @can('generate_estimate')
                    <button type="button" class="deleteNote trans-danger-btn"
                        data-bs-dismiss="modal">Delete</button>
                @endcan
                <button type="button" class=" invoice-pg-2-cls" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>
<form id="addEstimateNote" method='' action="">
    @csrf
    <div class="operation_modal modal fade" id="editNoteModal" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="editNoteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editNoteModalLabel">Edit Note</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body pb-0">
                    <textarea class="wycwyg_editor addNoteSummer" name="description" id="addNoteSummer" style="height:200px">
                    {!! $generateEstimate?->notes !!}
                    </textarea>
                </div>
                <div id="invalid-note"></div>

                <div class="modal-footer gap-4 mb-3">


                    <button type="button" class="cancelModal invoice-pg-2-cls"
                        data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="saveNotes" date-value="updated"
                        class=" invoice-pg-2-sv">Update</button>
                </div>
            </div>
        </div>
    </div>
</form>
@else
<form id="addEstimateNote" method='' action="">
    @csrf
    <div class="operation_modal modal fade" id="addNoteModal" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="addNoteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addNoteModalLabel">Add Note</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body pb-0">
                    <textarea class="wycwyg_editor addNoteSummer" name="description" id="addNoteSummer" style="height:200px"></textarea>
                </div>
                <div id="invalid-note"></div>

                <div class="modal-footer gap-4 mb-3">


                    <button type="button" class=" invoice-pg-2-cls cancelModal"
                        data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="saveNotes" date-value="added"
                        class=" invoice-pg-2-sv">Save</button>
                </div>
            </div>
        </div>
    </div>
</form>
@endif
