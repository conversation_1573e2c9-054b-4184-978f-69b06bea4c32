@extends('layouts.admin.master')
@section('title', 'Estimates')
@section('section')
<style>
    .select2-container--default{
        margin-top: 21px !important;
    }
    .table_filter_dropdown{
        margin-top: -45px !important;
    }
    .hidemodelbtn {
        font-size: 18px !important;
        color: #7e8a9d !important;
    }
    .hidemodelbtn:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .hidemodelbtn:hover {
        cursor: pointer !important;
    }
    @media screen and (max-width: 580px) {
        .hidemodelbtn {
            font-size: 15px !important;
            color: #7e8a9d !important;
        }
    }
</style>
    <section class="dashboard_main pb-5 estimates_table_filters">

        <div class="table_filter_header mb-4">
            <h2 class="sub_heading">Archive Estimates</h2>

            <div class="filters">
                <input type="search" placeholder="Search" name="" id="filter_search"
                    class="clients_Detail_Search filter_search">
                <select name="" id="select_filter" class="table_filter_select select-small custom_selectBox">
                    <option value="" selected>Filter</option>
                    <option value="Clear">Clear</option>
                    <option value="won">Won</option>
                    <option value="lost">Lost</option>
                    <option value="proposed">Proposed</option>
                    <option value="revision">Revision</option>
                </select>
            </div>
        </div>


        <div class="table-responsive">
            <table id="clients_Detail" class="table table-striped custom_datatable yajra-datatable display"
                style="width:100%">
                <thead>
                    <tr>
                        <th>ER #</th>
                        <th>Project Name</th>
                        <th>Client Name</th>
                        <th>Salesman Name</th>
                        <th>Estimator Name</th>
                        {{-- <th>Total Cost</th>
                        <th>Total Price</th> --}}
                        {{-- <th>Note</th> --}}
                        <th>Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>


            </table>
        </div>


    </section>
    @include('layouts.admin.confirmation-modal')
    @include('layouts.partials.success-modal')

    <!--Edit Modal -->
    <div class="operation_modal modal fade" id="editTableModal" tabindex="-1" aria-labelledby="editTableModalLabel"
        aria-hidden="true">
        <div class="modal-dialog m400">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTableModalLabel">Edit</h5>
                    <button
                    type="button"
                    class="btn-close px-3 hidemodelbtn"
                    data-bs-dismiss="modal"
                    aria-label="Close"
                    style="
                        border: none;
                        background-color: transparent;
                        font-size: 21px;
                    "
                >
                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                </button>
                </div>
                <div class="modal-body">
                    <h2 class="text-md text-primary mb-3">Change Status</h2>
                    <p class="text-placeholder text-sm">You can change estimate status</p>

                    <div class="row">
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status" value="won" id="won">
                                <label class="label" for="won">Won</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status" value="proposed" id="proposed">
                                <label class="label" for="proposed">Proposed</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status" value="lost" id="lost">
                                <label class="label" for="lost">Lost</label>
                            </div>
                        </div>
                    </div>

                    <div id="cancelReason">
                        <h2 class="text-md text-primary mb-3 mt-5">Reason</h2>
                        <p class="text-placeholder text-sm">Before you cancel, Please let us know the reason job was lost.
                        </p>

                        <div class="radio_group mt-5">
                            <input class="" type="radio" name="lost_reason" value="pricing" id="pricing">
                            <label class="label" for="price">Price</label>
                        </div>

                        <div class="radio_group mt-5">
                            <input class="" type="radio" name="lost_reason" value="competition" id="competition">
                            <label class="label" for="competition">Competition</label>
                        </div>

                        <div class="radio_group mt-5">
                            <input class="" type="radio" value="budget" name="lost_reason" id="budget">
                            <label class="label" for="budget">Budget</label>
                        </div>

                        <div class="radio_group mt-5">
                            <input class="" type="radio" value="timing" name="lost_reason" id="timing">
                            <label class="label" for="timing">Timing</label>
                        </div>

                        <div class="radio_group mt-5">
                            <input class="" type="radio" value="poorQualification" name="lost_reason"
                                id="poorQualification">
                            <label class="label" for="poorQualification">Poor Qualification</label>
                        </div>

                        <div class="radio_group mt-5">
                            <input class="" type="radio" value="unresponsive" name="lost_reason"
                                id="unresponsive">
                            <label class="label" for="unresponsive">Unresponsive</label>
                        </div>

                        <div class="radio_group mt-5">
                            <input class="" type="radio" value="noDecision" name="lost_reason" id="noDecision">
                            <label class="label" for="noDecision">No Decision</label>
                        </div>

                        <div class="radio_group mt-5">
                            <input class="" type="radio" name="lost_reason" value="other" id="other">
                            <label class="label" for="other">Other <span class="placeholder-text font-14">(Please
                                    explain
                                    below)</span></label>
                        </div>
                        <input type="hidden" name="reason" />

                        <textarea class="input mt-5" placeholder="Anything you want to share?" name="suggestion" id="suggestion"
                            value="" cols="30" rows="4"></textarea>
                    </div>


                </div>
                <div class="modal-footer pt-5">
                    <button type="button" class="btn primaryblue updateStatus w-100">Update</button>
                </div>
            </div>
        </div>
    </div>


    <!-- View Note Modal -->
    <div class="modal fade propert-modal" id="ViewNoteModal" data-bs-backdrop="static" data-bs-keyboard="false"
        tabindex="-1" aria-labelledby="requestChangeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="requestChangeModalLabel">View Note</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                    <div class="body-wraped">
                        <div class="filed-wraper" id="Notes-preview">
                            {{-- <textarea class="modal-textarea" name="reason_for_change" id="Notes-preview" placeholder="Enter you reason"
                                readonly></textarea> --}}
                        </div>
                        <button type="button" class="add-btn" data-bs-dismiss="modal" aria-label="Close">Close</button>

                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Change Requests Modal -->
    <div class="modal fade propert-modal" id="requestChangeModal" data-bs-backdrop="static" data-bs-keyboard="false"
        tabindex="-1" aria-labelledby="requestChangeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="requestChangeModalLabel">Change Estimate Request</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                    <div class="body-wraped">
                        {{-- <div class="filed-wraper">
                            <label for="price" class="modal-label">Your price</label>
                            <input class="modal-field" type="text" id="change-request-price" name="change_price" readonly>
                        </div> --}}

                        <div class="filed-wraper">
                            <label for="address" class="modal-label">Reason for changes</label>
                            <textarea class="modal-textarea" name="reason_for_change" id="change-request-reason" readonly></textarea>
                        </div>
                        @can('generate_estimate')
                            <a href="" style="text-decoration:none" class="add-btn edit-change-request-btn">Edit
                                Estimate</a>
                        @endcan
                    </div>
                </div>

            </div>
        </div>
    </div>


    <div class="modal-small Delete-modal modal fade" id="archiveModal" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="archiveModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/quetion.svg') }}"
                            alt="check icon">
                    </div>
                    <div class="dynamic-content-data">

                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <button type="button" class="btn primaryblue transparent w-100 mt-5 "
                                data-bs-dismiss="modal">Cancel</button>
                        </div>
                        <div class="col-sm-6">
                            <button type="button" class="btn primaryblue w-100 mt-5 archiveConfirmation">Yes</button>
                        </div>
                    </div>

                </div>

            </div>
        </div>
    </div>



    @push('scripts')
        @include('organization.generate-estimate.archive-script')
        <script>
            $(document).on('click', '.viewNoteBtn', function(e) {
                var id = $(this).attr('data-id');
                var url = "{{ route('organization.estimate.notes-view', ':id') }}",
                    url = url.replace(':id', id);
                $.ajax({
                    url: url,
                    type: "GET",
                    dataType: "json",
                    success: function(response) {
                        $('#Notes-preview').html(response.notes.notes);
                        $('#ViewNoteModal').modal('show');
                    },
                    error: function(response) {

                    }

                });
            })

            $(document).on('click', '.changeRequestBtn', function(e) {
                var id = $(this).attr('data-id');
                var url = '{{ URL::route(getRouteAlias() . '.estimate.view-change-request', ':id') }}',
                    url = url.replace(':id', id);
                $.ajax({
                    url: url,
                    type: "GET",
                    dataType: "json",
                    success: function(response) {
                        console.log('response', response);
                        $('#change-request-price').val(response.estimate.generate_estimate.client_action
                            .price);
                        $('#change-request-reason').html(response.estimate.generate_estimate.client_action
                            .description)
                        var url = '{{ URL::route(getRouteAlias() . '.create.estimate-generate', ':id') }}',
                            url = url.replace(':id', response.estimate.request_id);
                        $('.edit-change-request-btn').attr('href', url);


                        $('#requestChangeModal').modal('show');
                    },
                    error: function(response) {

                    }

                });
            })
        </script>
    @endpush
@endsection
