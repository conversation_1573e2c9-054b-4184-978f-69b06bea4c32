<script type="text/javascript">
    var status = '';
    var updateId = '';
    var reason = '';
    var deleteId;
    var archiveId;
    var estimates_table;

    $(document).ready(function() {
        estimates_table = $('.yajra-datatable').DataTable({
            processing: true,
            responsive: true,
            scrollX: true,
            serverSide: true,
            ajax: {
                url: "{{ route(getRouteAlias() . '.generate-estimate.archive-list') }}",
                data: function(d) {
                    d.status = status
                }
            },
            columns: [{
                    data: 'er_no',
                    name: 'er_no',
                    orderable: false
                }, {
                    data: 'opportunity_name',
                    name: 'opportunity_name',
                    orderable: false
                }, {
                    data: 'client.full_name',
                    name: 'client.full_name',
                    orderable: false
                },
                {
                    data: 'sale_man.name',
                    name: 'sale_man.name',
                    orderable: false
                }, {
                    data: 'estimator.name',
                    name: 'estimator.name',
                    orderable: false
                },
                //  {
                //     data: 'generateEstimate.total_cost',
                //     name: 'generateEstimate.total_cost',
                //     orderable: false
                // }, {
                //     data: 'generateEstimate.total_price',
                //     name: 'generateEstimate.total_price',
                //     orderable: false
                // },
                // {
                //     data: 'generateEstimate.notes',
                //     name: 'generateEstimate.notes',
                //     orderable: false
                // },
                {
                    data: 'generateEstimate.status',
                    name: 'generateEstimate.status',
                    orderable: false
                },
                {
                    data: 'action',
                    name: 'action',
                    orderable: false
                }


            ],
            language: {
                zeroRecords: "Sorry we could not find any results",
                paginate: {
                    "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                    "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                },


            },
            dom: '<"top">rt<"bottom"lip><"clear">',
        });

        // Custome Search Filter
        $('.estimates_table_filters #filter_search').on('keyup', function() {
            estimates_table.search(this.value).draw();
        });

        // Custom Select Filter
        $('.estimates_table_filters #select_filter').on('change', function() {
            var selectedValue = $(this).val();
            status = selectedValue;
            console.info(status);
            estimates_table.search(status).draw();
        });
    });
    $(document).on('click', '#changeStatus', function() {
        $('.invalid-feedback').html('');
        var dataStatus = $(this).attr("data-value");
        $("#" + dataStatus).prop("checked", true);
        updateId = $(this).attr("data-id");
        if (dataStatus == 'lost') {
            status = 'lost';
            $('#cancelReason').show();
            var dataReason = $(this).attr("data-reason");
            reason = dataReason;
            var dataSuggestion = $(this).attr("data-suggestion");
            if (dataReason) {
                $("#" + dataReason).prop("checked", true);
            }
            $('#suggestion').val(`${dataSuggestion}`);


        } else {
            $('#cancelReason').hide();

        }

        console.log("reason", reason);

    })
    $(document).on('click', '#lost', function() {
        $('#cancelReason').show();
        status = 'lost';
        $("#" + reason).prop("checked", false)
        reason = '';
    })

    $(document).on('click', '#proposed', function() {
        $('#cancelReason').hide();
        status = 'proposed';
        reason = '';
        $('#suggestion').val('')
    })

    $(document).on('click', '#won', function() {
        $('#cancelReason').hide();
        status = 'won';
        reason = '';
        $('#suggestion').val('')
    })
    $(document).on('click',
        '#price , #competition , #budget ,#timing ,#poorQualification ,#unresponsive ,#noDecision ,#other',
        function() {
            reason = $(this).val();
            $('.invalid-feedback').html('');

        })
    $(document).on('click', '.updateStatus', function() {
        $('.invalid-feedback').html('');
        reason = $('[name="lost_reason"]:checked').val();
        if (reason == undefined) {
            reason = '';
        }
        if (reason == 'other' && !$('#suggestion').val()) {
            $(document).find('[name=suggestion]').after(
                '<div class="invalid-feedback laravel_error" >' + 'This field is required' +
                '</div>');
            $(".invalid-feedback").css("display", "block");
            return;
        }
        if (status == 'lost' && reason == '') {
            $(document).find('[name=reason]').after(
                '<div class="invalid-feedback laravel_error" >' + 'Please select any reason' +
                '</div>');
            $(".invalid-feedback").css("display", "block");
            return;
        }
        var url =
            '{{ URL::route(getRouteAlias() . '.update-estimate-generate-status') }}';

        if (status) {
            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    "_token": "{{ csrf_token() }}",
                    status: status,
                    id: updateId,
                    reason: reason,
                    suggestion: $('#suggestion').val()
                },

                success: function(response) {
                    status = '';
                    $('#editTableModal').modal('hide');
                    estimates_table.draw();
                },
                error: function(response) {
                    status = '';
                }
            })
        }
    })


    $(document).on('click', '#deleteRequest', function(e) {
        $('.dynamic-content-data').html('');
        $('.dynamic-content-data').append(
            `<h2 class="title text-center">Delete Request</h2>
         <p class="para mt-3 text-center">Are you sure you want to delete this estimate?</p>`
        );
        deleteId = $(this).data("value");
    });


    $(document).on('click', '.archiveBtn', function(e) {
        $('.dynamic-content-data').html('');
        $('.dynamic-content-data').append(
            `<h2 class="title text-center">Archive Estimate</h2>
         <p class="para mt-3 text-center">Are you sure you want to archive this estimate?</p>`
        );
        archiveId = $(this).data("id");
        $('#archiveModal').modal('show');
    });

    $('.archiveConfirmation').on('click', function() {
        archiveEstimate();
    })

    function archiveEstimate() {
        var url = "{{ URL::route(getRouteAlias() . '.estimate.archive', ':id') }}",
            url = url.replace(':id', archiveId);

        $.ajax({
            method: "POST",
            url: url,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            success: function(response) {
                estimates_table.draw();
                $('#archiveModal').modal('hide');
                toastr.success('Estimate Archive Successfully!');
            },
            error: function(response) {
                $('#archiveModal').modal('hide');
                toastr.error('Estimate Does not  Archive Successfully!');
            }
        })
    }

    $('.deleteConfirmation').on('click', function() {
        deleteData();
    })

    function deleteData() {
        var url = "{{ URL::route(getRouteAlias() . '.estimate-generate.delete', ':id') }}",
            url = url.replace(':id', deleteId);

        $.ajax({
            method: "DELETE",
            url: url,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            success: function(response) {
                estimates_table.draw();
                $('#DeleteModal').modal('hide');
                $('#successModal').modal('show');
                $('.dynamic-success-data').html('');
                $('.dynamic-success-data').append(
                    ` <h2 class="title text-center">Done</h2>
                 <p class="para mt-3 text-center">Estimate Deleted Successfully!</p>`
                );
            },
            error: function(response) {
                $('#DeleteModal').modal('hide');
            }
        })
    }
</script>
