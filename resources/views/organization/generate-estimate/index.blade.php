@extends('layouts.admin.master')
@section('title', 'Generate Estimate')
@section('section')
<style>
    .dropdown-toggle::after {
    display: none !important;

}
</style>
    <section class="dashboard_main pb-5">

        <div class="panel mt-4">
            <div class="panel_header">
                <h2 class="panel_title">Request Details</h2>
                <p class="text"><span class="placeholder-text">Request #: </span>{{ $estimate->job_no ?? '' }}</p>
            </div>

            <!-- Request Detail Info -->
            <!-- <div class="row mt-5">
                                                                                                                                                                                                                                            <div class="col-lg-4 col-md-6">
                                                                                                                                                                                                                                                <div class="row">
                                                                                                                                                                                                                                                    <div class="col-sm-6">
                                                                                                                                                                                                                                                        <div class="detail_info mt-4">
                                                                                                                                                                                                                                                            <label for="" class="label">Client Name</label>
                                                                                                                                                                                                                                                            <p class="text">{{ $estimate->client?->full_name }}</p>
                                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                    <div class="col-sm-6">
                                                                                                                                                                                                                                                        <div class="detail_info mt-4">
                                                                                                                                                                                                                                                            <label for="" class="label">Company Name</label>
                                                                                                                                                                                                                                                            <p class="text">{{ $estimate->client?->company_name }} </p>
                                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                    <div class="col-sm-6">
                                                                                                                                                                                                                                                        <div class="detail_info mt-4">
                                                                                                                                                                                                                                                            <label for="" class="label">Phone Number</label>
                                                                                                                                                                                                                                                            <p class="text">{{ $estimate->client?->mobile_no }}</p>
                                                                                                                                                                                                                                                            @if ($estimate->client?->alternate_no)
    <p class="text">{{ $estimate->client?->alternate_no }}</p>
                                                                                                                                                                                                                                                                                            <p class="placeholder-text font-14">(Alternate)</p>
    @endif
                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                <div class="col-sm-6">
                                                                                                                                                                                                                                                                                    <div class="detail_info mt-4">
                                                                                                                                                                                                                                                                                        <label for="" class="label">Email</label>
                                                                                                                                                                                                                                                                                        <p class="text">{{ $estimate->client?->email }}</p>
                                                                                                                                                                                                                                                                                        @if ($estimate->client?->alternate_email)
    <p class="text">{{ $estimate->client?->alternate_email }}</p>
                                                                                                                                                                                                                                                                                            <p class="placeholder-text font-14">(Alternate)</p>
    @endif
                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                <div class="col-sm-6">
                                                                                                                                                                                                                                                                                    <div class="detail_info mt-4">
                                                                                                                                                                                                                                                                                        <label for="" class="label">Office Number</label>
                                                                                                                                                                                                                                                                                        <p class="text">{{ $estimate->client?->office_no }}</p>
                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                            </div>
                                                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                                                        <div class="col-lg-4 col-md-6">
                                                                                                                                                                                                                                                                            <div class="row">
                                                                                                                                                                                                                                                                                <div class="col-sm-6">
                                                                                                                                                                                                                                                                                    <div class="detail_info mt-4">
                                                                                                                                                                                                                                                                                        <label for="" class="label">Project Name</label>
                                                                                                                                                                                                                                                                                        <p class="text">{{ $estimate->project_name }}</p>
                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                <div class="col-sm-6">
                                                                                                                                                                                                                                                                                    <div class="detail_info mt-4">
                                                                                                                                                                                                                                                                                        <label for="" class="label">Lead Source</label>
                                                                                                                                                                                                                                                                                        <p class="text">{{ $estimate->lead_source }}</p>
                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                <div class="col-sm-6">
                                                                                                                                                                                                                                                                                    <div class="detail_info mt-4">
                                                                                                                                                                                                                                                                                        <label for="" class="label">Salesperson Name</label>
                                                                                                                                                                                                                                                                                        <p class="text">{{ $estimate->saleMan?->name }}</p>
                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                <div class="col-sm-6">
                                                                                                                                                                                                                                                                                    <div class="detail_info mt-4">
                                                                                                                                                                                                                                                                                        <label for="" class="label">Probability</label>
                                                                                                                                                                                                                                                                                        <p class="text">{{ $estimate->probability }}</p>
                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                <div class="col-sm-6">
                                                                                                                                                                                                                                                                                    <div class="detail_info mt-4">
                                                                                                                                                                                                                                                                                        <label for="" class="label">Estimator Name</label>
                                                                                                                                                                                                                                                                                        <p class="text">{{ $estimate->estimator?->name }}</p>
                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                            </div>
                                                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                                                        <div class="col-lg-4 col-md-6">
                                                                                                                                                                                                                                                                            <div class="detail_info mt-4">
                                                                                                                                                                                                                                                                                <label for="" class="label">Property</label>
                                                                                                                                                                                                                                                                                <p class="text">{{ $estimate->propertyAddress?->property_name }}</p>
                                                                                                                                                                                                                                                                            </div>

                                                                                                                                                                                                                                                <div class="detail_info mt-4">
                                                                                                                                                                                                                                                    <label for="" class="label">Address 1</label>
                                                                                                                                                                                                                                                    <p class="text">{{ $estimate->propertyAddress?->address1 }}</p>
                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                            </div>
                                                                                                                                                                                                                                        </div> -->
            <!-- Request Detail Info -->
            <div class="row gy-4 pt-4 pb-4 border-bottom">
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Client Name</label>
                        <p class="text">{{ $estimate->client?->full_name }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Company Name</label>
                        <p class="text">{{ $estimate->client?->company_name ?? '---' }} </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Property</label>
                        <p class="text">{{ $estimate->propertyAddress?->property_name }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Address 1</label>
                        <p class="text">{{ $estimate->propertyAddress?->address1 }}</p>
                    </div>
                </div>
            </div>
            <div class="row gy-4 pt-4 pb-4 border-bottom">
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Address 2</label>
                        <p class="text">{{ $estimate->propertyAddress?->address2 ?? '-----'}}</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">City</label>
                        <p class="text">{{ $estimate->propertyAddress?->city }}</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">State</label>
                        <p class="text">{{ $estimate->propertyAddress?->state }}</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Zip Code</label>
                        <p class="text">{{ $estimate->propertyAddress?->zip }}</p>
                    </div>
                </div>
            </div>
            <div class="row gy-4 pt-4 pb-4 border-bottom">
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Phone Number</label>
                        <p class="text">{{ $estimate->client?->mobile_no }}</p>
                        @if ($estimate->client?->alternate_no)
                            <p class="text">{{ $estimate->client?->alternate_no }}</p>
                            <p class="placeholder-text font-14">(Alternate)</p>
                        @endif
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Email</label>
                        <p class="text">{{ $estimate->client?->email }}</p>
                        @if ($estimate->client?->alternate_email)
                            <p class="text">{{ $estimate->client?->alternate_email }}</p>
                            <p class="placeholder-text font-14">(Alternate)</p>
                        @endif
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Project Name</label>
                        <p class="text">{{ $estimate->project_name }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Lead Source</label>
                        <p class="text">{{ $estimate->lead_source }}</p>
                    </div>
                </div>
            </div>
            <div class="row gy-4 pt-4 pb-4">
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Estimator Name</label>
                        <p class="text">{{ $estimate->estimator?->name ?? '---' }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Salesperson Name</label>
                        <p class="text">{{ $estimate->saleMan?->name ?? '---' }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Work Type</label>
                        <select name="equipment_id" id="selectEquipmentId" onchange="setServiceName(this);"
                            class="input custom_selectBox basic-single-select-search p-0" style="width: 90%;border:none;height:fit-content">
                            <option value="" selected disabled>Select
                                work type</option>
                            @forelse ($workTypes as $item)
                                <option
                                    {{ !empty($generate_estimate) ? ($generate_estimate->work_type_id == $item->id ? 'selected' : '') : '' }}
                                    id="{{ encodeID($item->id) }}" value="{{ encodeID($item->id) }}"
                                    data-service-id="{{ encodeID($item->serviceLine->id) }}"
                                    data-service-name="{{ $item->serviceLine->name }}">
                                    {{ ucwords($item->name) }}</option>

                            @empty
                            @endforelse

                        </select>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="detail_info">
                        <label for="" class="label">Service Line</label>
                        <p class="text " id="service-line-text">
                            {{ !empty($generate_estimate) ? ($generate_estimate->work_type_id != null ? $workTypes->where('id', $generate_estimate->work_type_id)?->first()?->serviceLine->name : '---') : '---' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>


        <div id="smartwizard" class="estimate_wizard setup_wizard_stepper mt-4">
            <div class="nav_wrapper">
                <ul class="panel nav border-bottom-left-right-radius-none">
                    <li class="nav-item">
                        <a class="nav-link" href="#step-1">
                            <div class="num">1</div>
                            <div class="title">Equipment</div>
                            <div class="separator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#step-2">
                            <span class="num">2</span>
                            <div class="title">Labor</div>
                            <div class="separator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#step-3">
                            <span class="num">3</span>
                            <div class="title">Materials</div>
                            <div class="separator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " href="#step-4">
                            <span class="num">4</span>
                            <div class="title">Other cost</div>
                            <div class="separator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " href="#step-5">
                            <span class="num">5</span>
                            <div class="title">Sub - Contractor</div>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="tab-content">
                <!-- Step for Equipment -->
                <div id="step-1" class="tab-pane p-0" role="tabpanel" aria-labelledby="step-1">
                    <div class="panel border-top-left-right-radius-none">
                        <h2 class="text-md2 text-primary2 mb-3">Equipment</h2>
                        <form action="" id="step1Form" enctype="multipart/form-data">
                            @csrf
                            <div class="row row-gap-16">
                                <div class="col-lg-4 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Items Name <span class="steric">*</span></label>
                                        <div class="absolute-error selectEquipment ">
                                            <select name="equipment_id" id="selectEquipmentId" onchange="getval(this);"
                                                class="input custom_selectBox basic-single-select-search selectEquipment">
                                                <option value="" selected disabled>Select
                                                    equipments</option>
                                                @forelse ($equipments as $item)
                                                    <option id={{ $item->id }} value="{{ $item->id }}"
                                                        data-uom={{ $item->uom }} data-cost="{{ $item->cost }}"
                                                        data-margin="{{ $equipmentMargin->default ?? null }}">
                                                        {{ $item->name }}</option>

                                                @empty
                                                @endforelse

                                            </select>
                                            <div id="invalid-equipment">

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <div class="field uom">
                                        <label class="label mb-2">UoM <span class="steric">*</span></label>
                                        <div class="absolute-error">
                                            <input type="text" name='uom' placeholder="UoM" id="equipment-uom"
                                                class="input form-control" @disabled(true)>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Quantity<span class="steric">*</span></label>
                                        <input type="number" name="quantity" id="equipment-quantity" value=""
                                            placeholder="01" class="input form-control" min="0" max="100">
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <label class="label mb-2">Unit Cost<span class="steric">*</span></label>
                                    <div class="field unitCost position-relative">
                                        <input type="number" id="unitCost" placeholder="00"
                                            @disabled(true) name="unit_cost" value=""
                                            class="input form-control" min="0" max="99999999">
                                        <span class="dollar">$</span>
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Gross margin<span class="steric">*</span></label>
                                        <input type="number" name="gross_margin" id="equipment-gross-margin"
                                            value="" placeholder="50%" class="input form-control" min="0"
                                            max="100">
                                    </div>
                                </div>
                                <input type="hidden" name="request_id" value="{{ $estimate->id }}">
                            </div>

                            <div class="buttons d-flex align-items-center justify-content-end flex-wrap gap-3 mt-4">
                                <button type="button" id="addEquipment" class="btn primaryblue transparent">Add
                                    Equipment</button>
                            </div>
                        </form>
                    </div>
                    {{-- <x-estimate_components.estimate_equipment /> --}}
                    <div id="equipmentTable">

                    </div>

                </div>
                <!-- Step for Equipment -->

                <!-- Step for Labor -->
                <div id="step-2" class="tab-pane p-0" role="tabpanel" aria-labelledby="step-2">

                    {{-- <x-estimate_components.estimate_labor /> --}}
                    <div class="panel border-top-left-right-radius-none">
                        <h2 class="text-md2 text-primary2 mb-3">Labor</h2>
                        <form action="" id="step2Form" enctype="multipart/form-data">
                            @csrf
                            <div class="row row-gap-16">

                                <div class="col-lg-2 col-md-4">

                                    <div class="field">
                                        <label class="label mb-2">Items Name <span class="steric">*</span></label>
                                        <div class="absolute-error selectLabor">
                                            <select name="labor_id" id="laborId" onchange="getLaborDetail(this);"
                                                class="input custom_selectBox basic-single-select-search">
                                                <option value="" selected disabled>Select Items</option>
                                                @forelse ($labors as $item)
                                                    <option value="{{ $item->id }}" data-uom={{ $item->uom }}
                                                        data-cost="{{ $item->cost }}"
                                                        data-margin="{{ $laborMargin->default ?? null }}">
                                                        {{ $item->name }}</option>
                                                @empty
                                                @endforelse
                                            </select>
                                            <div id="invalid-labor">

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Enter Description<span class="steric">*</span></label>
                                        <input type="text" name="description" id="laborDescription" value=""
                                            placeholder="Enter Description" class="input form-control">
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <div class="field laborUom">
                                        <label class="label mb-2">UoM <span class="steric">*</span></label>
                                        <div class="absolute-error">
                                            <input type="text" name='labor_uom' placeholder="UoM" value=""
                                                id="labor-uom" class="input form-control" @disabled(true)>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Quantity<span class="steric">*</span></label>
                                        <input type="number" name="labor_quantity" id="laborQuantity" value=""
                                            placeholder="01" class="input form-control" min="0" max="100">
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4 laborCost">
                                    <label class="label mb-2">Unit Cost<span class="steric">*</span></label>
                                    <div class="field position-relative">
                                        <input type="number" name="labor_cost" placeholder="00" id="labor-cost"
                                            @disabled(true) class="input form-control" min="0"
                                            max="99999999">
                                        <span class="dollar">$</span>
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Gross margin<span class="steric">*</span></label>
                                        <input type="number" name="labor_gross_margin" id="laborMargin" value=""
                                            placeholder="50%" class="input form-control" min="0" max="100">
                                    </div>
                                </div>
                                <input type="hidden" name="request_id" value="{{ $estimate->id }}">
                            </div>

                            <div class="buttons d-flex align-items-center justify-content-end flex-wrap gap-3 mt-4">
                                <button type="button" id="addLabor" class="btn primaryblue transparent">Add
                                    Labor</button>
                            </div>
                        </form>
                    </div>
                    <div id="laborTable">

                    </div>
                </div>
                <!-- Step for Labor -->

                <!-- Step for Materials -->
                <div id="step-3" class="tab-pane p-0" role="tabpanel" aria-labelledby="step-3">
                    <div class="panel border-top-left-right-radius-none">
                        <h2 class="text-md2 text-primary2 mb-3">Material</h2>
                        <form action="" id="step3Form" enctype="multipart/form-data">
                            @csrf
                            <div class="row">
                                <div class="col-12">
                                    <div class="field">
                                        <div class="stepper_radio_tabs nav nav-pills p-0 m-0" role="tablist">
                                            <input type="radio" class="btn-check" name="material_type"
                                                id="plant_material" autocomplete="off" checked>
                                            <label class="btn radio_tab_btn nav-link active"
                                                id="plantMaterial"for="plant_material" data-bs-toggle="pill"
                                                data-bs-target="#pills-plant-material">Plant
                                                Materials</label>

                                            <input type="radio" class="btn-check" name="material_type"
                                                id="hard_materials" autocomplete="off">
                                            <label class="btn radio_tab_btn nav-link" id="hardMaterial"
                                                for="hard_materials" data-bs-toggle="pill"
                                                data-bs-target="#pills-hard-material">Hard
                                                Materials</label>
                                        </div>
                                        <div class="tab-content" id="pills-tabContent ">
                                            <div class="tab-pane fade show active selectMaterial"
                                                id="pills-plant-material" role="tabpanel"
                                                aria-labelledby="pills-plant-material-tab">
                                                <select name="plant_material_id" id="selectedPlantMaterial"
                                                    onchange="getMaterialDetail(this)"
                                                    class="input custom_selectBox basic-single-select-search">
                                                    <option value="" selected disabled>Select Items</option>
                                                    @forelse ($plantMaterial as $item)
                                                        <option value="{{ $item->id }}"
                                                            data-uom="{{ $item->size }}"
                                                            data-cost="{{ $item->cost }}"
                                                            data-margin="{{ $plantMaterialMargin->default ?? '' }}"
                                                            data-type="plant">
                                                            {{ $item->name }}</option>
                                                    @empty
                                                    @endforelse
                                                </select>
                                                <div id="invalid-plant_material_id"></div>


                                            </div>
                                            <div class="tab-pane fade selectMaterial" id="pills-hard-material"
                                                role="tabpanel" aria-labelledby="pills-hard-material-tab">
                                                <select name="hard_material_id" id="selectedHardMaterial"
                                                    onchange="getMaterialDetail(this)"
                                                    class="input custom_selectBox basic-single-select-search">
                                                    <option value="" selected disabled>Select Items</option>
                                                    @forelse ($hardMaterial as $item)
                                                        <option value="{{ $item->id }}"
                                                            data-uom="{{ $item->uom }}"
                                                            data-cost="{{ $item->cost }}" data-type="hard"
                                                            data-margin="{{ $hardMaterialMargin->default ?? '' }}">
                                                            {{ $item->name }}</option>
                                                    @empty
                                                    @endforelse

                                                </select>
                                                <div id="invalid-material_id"></div>



                                            </div>

                                        </div>




                                    </div>


                                </div>
                            </div>
                            <hr class="my-4">
                            <div class="row row-gap-16">
                                <div class="col-lg-2 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">UoM</label>
                                        <input type="text" id="material-uom" name='material_uom' placeholder="UoM"
                                            value="" @disabled(true) class="input form-control">
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Quantity</label>
                                        <input type="number" id="material-quantity" placeholder="0"
                                            name="material_quantity" value="" class="input form-control"
                                            min="0" max="100">
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <label class="label mb-2">Unit Cost</label>
                                    <div class="field position-relative">
                                        <input type="number" id="material-cost" placeholder="00" name="material_cost"
                                            @disabled(true) class="input form-control" min="0"
                                            max="99999999">
                                        <span class="dollar">$</span>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Gross margin</label>
                                        <input type="number" name="material_gross_margin" id="materialMargin"
                                            value="" placeholder="50%" class="input form-control" min="0"
                                            max="100">
                                    </div>
                                </div>
                                <input type="hidden" name="request_id" value="{{ $estimate->id }}">

                            </div>

                            <div class="buttons d-flex align-items-center justify-content-end flex-wrap gap-3 mt-4">
                                <button type="button" id="addMaterial" class="btn primaryblue transparent ">Add
                                    Item</button>
                            </div>
                        </form>
                    </div>
                    <div id="materialTable"></div>
                    {{-- <x-estimate_components.estimate_materials /> --}}
                </div>
                <!-- Step for Materials -->

                <!-- Step for Other cost -->
                <div id="step-4" class="tab-pane p-0" role="tabpanel" aria-labelledby="step-4">
                    <div class="panel border-top-left-right-radius-none">
                        <h2 class="text-md2 text-primary2 mb-3">Other Job Cost</h2>
                        <form action="" id="step4Form" enctype="multipart/form-data">
                            @csrf
                            <div class="row row-gap-16">
                                <div class="col-lg-2 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Select items <span class="steric">*</span></label>
                                        <div class="absolute-error selectCost">
                                            <select name="other_cost_id" id="other-cost-id"
                                                onchange="getCostDetail(this)"
                                                class="input custom_selectBox basic-single-select-search">
                                                <option value="" selected disabled>Select Items</option>
                                                @forelse ($otherCost as $item)
                                                    <option value="{{ $item->id }}" data-uom="{{ $item->uom }}"
                                                        data-cost="{{ $item->cost }}"
                                                        data-margin="{{ $otherCostMargin->default ?? null }}">
                                                        {{ $item->name }}</option>
                                                @empty
                                                @endforelse

                                            </select>
                                        </div>
                                        <div id="invalidOtherCost"></div>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Description</label>
                                        <input type="text" placeholder="Enter Description" name="cost_description"
                                            id="other-cost-description" class="input form-control">
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">UoM<span class="steric">*</span></label>
                                        <input type="text" @disabled(true) name="cost_uom" id="cost-uom"
                                            placeholder="UoM" class="input form-control">
                                    </div>
                                </div>

                                <div class="col-lg-2 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Quantity<span class="steric">*</span></label>
                                        <input type="number" name="cost_quantity" id="other-cost-quantity"
                                            placeholder="01" class="input form-control" min="0"
                                            onkeyup="validateInput(this)">

                                    </div>
                                </div>

                                <div class="col-lg-3 col-md-4">
                                    <label class="label mb-2">Unit Cost <span class="steric">*</span></label>
                                    <div class="field position-relative">
                                        <input type="number" id="cost-unit-cost" name="cost_unit_cost"
                                            @disabled(true) placeholder="Enter unit cost"
                                            class="input form-control" min="0" max="99999999">
                                        <span class="dollar">$</span>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Gross Margin<span class="steric">*</span></label>
                                        <input type="number" id="cost-gross-margin" name="cost_gross_margin"
                                            placeholder="0%" class="input form-control" min="0" max="100">
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="request_id" value="{{ $estimate->id }}">

                            <div class="buttons d-flex align-items-center justify-content-end flex-wrap gap-3 mt-4">
                                <button type="button" id="addOtherCost" class="btn primaryblue transparent">Add
                                    Cost</button>
                            </div>
                        </form>
                    </div>
                    <div id="otherCostTable"></div>
                    {{-- <x-estimate_components.estimate_othercost /> --}}
                </div>
                <!-- Step for Other cost -->

                <!-- Step for Sub - Contractor -->
                <div id="step-5" class="tab-pane p-0" role="tabpanel" aria-labelledby="step-5">
                    <div class="panel border-top-left-right-radius-none">
                        <h2 class="text-md2 text-primary2 mb-3">Sub-Contractor</h2>
                        <form action="" id="step5Form" enctype="multipart/form-data">
                            @csrf
                            <div class="row row-gap-16">
                                <div class="col-lg-4 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Description</label>
                                        <input type="text" name="contractor_name" id="contractorName" value=""
                                            placeholder="Enter description" class="input form-control">
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">UoM<span class="steric">*</span></label>
                                        <input type="text" name="contractor_uom" id="contractorUom" value=""
                                            placeholder="UoM" class="input form-control">
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Quantity<span class="steric">*</span></label>
                                        <input type="number" name="contractor_quantity" id="contractorQuantity"
                                            value="" placeholder="01" class="input form-control" min="0"
                                            max="100">
                                    </div>
                                </div>

                                <div class="col-lg-2 col-md-4">
                                    <label class="label mb-2">Unit Cost <span class="steric">*</span></label>
                                    <div class="field position-relative">
                                        <input type="number" name="contractor_cost" id="contractorCost" value=""
                                            placeholder="00" class="input form-control" min="0" max="99999999">
                                        <span class="dollar">$</span>
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-4">
                                    <div class="field">
                                        <label class="label mb-2">Gross Margin<span class="steric">*</span></label>
                                        <input type="number" name="contractor_gross_margin"
                                            value="{{ $contractorMargin->default ?? null }}" id="contractorMargin"
                                            value="" placeholder="0%" class="input form-control" min="0"
                                            max="100">
                                    </div>
                                </div>
                                <input type="hidden" name="request_id" value="{{ $estimate->id }}">

                            </div>

                            <div class="buttons d-flex align-items-center justify-content-end flex-wrap gap-3 mt-4">
                                <button type="button" id="addSubContractor" class="btn primaryblue transparent">Add
                                    Sub-Contractor</button>
                            </div>
                    </div>
                    <div class="panel mt-4">
                        <h3 class="h3">Sub-Contractor</h3>
                        <hr class="my-4">
                        <div id="contractorTable"></div>
                    </div>
                </div>

                {{-- <x-estimate_components.estimate_sub_contractor /> --}}
                {{-- </div> --}}
                <!-- Step for Sub - Contractor -->

            </div>

        </div>



        <div class="d-flex justify-content-between gap-3 flex-wrap mt-5">
            <a type="reset" href="{{ route(getRouteAlias() . '.estimate.index') }}"
                class="btn transparent bg-transparent px-5">Cancel</a>


            <div class="wizard_buttons d-flex justify-content-between gap-3 flex-wrap">
                <button type="submit" class="btn primaryblue transparent px-5 wizard_back">Back</button>
                <button type="button" data-id={{ $estimate->id }} class="btn primaryblue px-5 wizard_next">Next</button>
                <a href={{ route(getRouteAlias() . '.estimate.invoice-detail', encodeId($estimate->id)) }} type="button"
                    class="btn primaryblue" id="wizard_generate_btn">Next</a>
            </div>
        </div>


    </section>
    @push('scripts')
        @include('organization.generate-estimate.script')
    @endpush
@endsection
