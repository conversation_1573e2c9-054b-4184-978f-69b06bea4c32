<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <title>Estimate Invoice</title>
</head>
<style>
    @font-face {
        font-family: 'Poppins';
        src: url({{ storage_path('fonts/Poppins/Poppins-Regular.ttf') }}) format("truetype");
        font-weight: 400;
        font-style: normal;
    }

    @font-face {
        font-family: 'Poppins';
        src: url({{ storage_path('fonts/Poppins/Poppins-Bold.ttf') }}) format("truetype");
        font-weight: 700;
        font-style: normal;
    }

    @font-face {
        font-family: 'Poppins';
        src: url({{ storage_path('fonts/Poppins/Poppins-SemiBold.ttf') }}) format("truetype");
        font-weight: 600;
        font-style: normal;
    }

    @font-face {
        font-family: 'Poppins';
        src: url({{ storage_path('fonts/Poppins/Poppins-Medium.ttf') }}) format("truetype");
        font-weight: 500;
        font-style: normal;
    }


    .w-100 {
        width: 100%;
    }

    .w-70 {
        width: 70%;
    }

    .w-50 {
        width: 50%;
    }

    .w-30 {
        width: 30%;
    }

    .float-left {
        float: left;
    }

    .float-right {
        float: right;
    }

    .mt-28 {
        margin-top: 28px;
    }

    .text-left {
        text-align: left;
    }

    .text-right {
        text-align: right;
    }

    .text-center {
        text-align: center;
    }

    body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
    }

    .top_bar .primary_header,
    .bottom_bar {
        height: 16px;
        width: 100%;
        background: {{ $primary_color }};
        position: relative;
    }

    .top_bar .primary_header_notch {
        transform: rotate(41.59deg);
        position: absolute;
        right: -59px;
        top: -17px;
    }

    .top_bar .primary_header_notch .notch_1 {
        height: 86px;
        width: 188px;
        background: {{ $secondary_color }};
    }

    .top_bar .primary_header_notch .notch_2 {
        height: 12px;
        width: 188px;
        background: {{ $primary_color }};
    }

    .header {
        margin-top: 28px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 153px;
    }

    .header .proposal_no {
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        text-transform: capitalize;
        color: #707683;
    }

    .header .proposal_no .value {
        color: #192a3e;
    }

    .placeholder-text {
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        text-transform: capitalize;
        color: #707683;
    }

    .title_name {
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        text-transform: capitalize;
        color: #003366;
    }

    .address,
    .zip_code {
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        text-transform: capitalize;
        color: #192a3e;
    }

    .main_heading {
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        text-transform: capitalize;
        color: #192a3e;
    }

    .spacing-0 {
        padding: 0px !important;
        margin: 0px !important;
    }

    .mt-4 {
        margin-top: 4px;
    }

    .mb-12 {
        margin-bottom: 12px;
    }

    .mt-16 {
        margin-top: 16px;
    }

    .mt-24 {
        margin-top: 24px;
    }

    .mt-49 {
        margin-top: 49px;
    }

    .vertical-top {
        vertical-align: top;
    }

    .vertical-middle {
        vertical-align: middle;
    }

    /* table style */

    .custom_datatable {
        box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px,
            rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
        background: #ffffff;
        border-radius: 8px;
    }

    .table_wrapper {
        border: 1px solid #e7e7e7;
        border-radius: 8px;
        overflow: hidden;
    }

    .custom_datatable thead th {
        /* padding: 16px !important; */
        vertical-align: middle;
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 22px;
        color: #192a3e;

        background: #dcf2ff;
        border-bottom: none !important;
        white-space: nowrap;
        user-select: none;
        margin: 0px;
        padding-left: 12px;
        padding-bottom: 12px;
        padding-right: 12px;
    }

    .custom_datatable thead th:first-child {
        border-top-left-radius: 8px;
    }

    .custom_datatable thead th:last-child {
        border-top-right-radius: 8px;
    }

    .custom_datatable tbody td {
        /* padding: 16px !important; */
        vertical-align: middle;
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: #192a3e;
        border: none !important;
        word-wrap: break-word;
        word-break: break-word;

        margin: 0px;
        padding-left: 12px;
        padding-bottom: 12px;
        padding-right: 12px;
    }

    .custom_datatable tbody tr:nth-child(even) td {
        background-color: #f9f9f9 !important;
    }

    .panel {
        background: #ffffff;
        border: 1px solid #e7e7e7;
        border-radius: 8px;
        padding: 16px;
    }

    .vertical_line {
        border: none;
        border-bottom: 1px solid #e7e7e7;
    }

    .leading-16 {
        line-height: 16px;
    }

    .mb-24 {
        margin-bottom: 24px;
    }

    .mb-16 {
        margin-bottom: 16px;
    }

    .mb-8 {
        margin-bottom: 8px;
    }

    .page-break {
        page-break-before: always;
    }

    .total_payable .title {
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        color: #192a3e;
    }

    .total_payable .value {
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 24px;
        line-height: 40px;
        color: #003366;
    }

    .approval_title {
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        text-transform: capitalize;
        color: #192a3e;
    }

    .approval_items .sign {
        margin-right: 16px;
    }

    .approval_items .signature {
        width: 100%;
        border-top: 1px dashed #90a0b7;
    }

    .approval_items .sign_title {
        /* font-family: "Poppins", sans-serif; */
        /* font-family:'Poppins'; */
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        text-transform: capitalize;
        color: #707683;
    }

    .mt-32 {
        margin-top: 32px;
    }

    /* table style end */
</style>

<body>
    <div class="top_bar">
        <div class="primary_header"></div>

        <div class="primary_header_notch">
            <div class="notch_1"></div>
            <div class="notch_2"></div>
        </div>
    </div>

    <div class="header w-100 mt-28">
        <table class="table w-100">
            <tr>
                <th class="w-50 float-left text-left">
                    <img height="47px" src="{{ asset('admin_assets/images/elmos-logo.png') }}" alt="site logo" />
                </th>
                <th class="w-50 float-left">
                    <p class="proposal_no w-100">
                        PROPOSAL# <span class="value">{{ $estimate->job_no ?? '' }}</span>
                    </p>
                </th>
            </tr>
        </table>
    </div>

    <div style="clear: both"></div>

    <div class="w-100 mt-28">
        <table class="table w-100">
            <tr>
                <th class="float-left spacing-0 vertical-top" style="width: 60%">
                    <p class="placeholder-text text-left spacing-0">
                        Recipient
                    </p>
                    <h2 class="title_name text-left spacing-0 mt-4">
                        {{ $estimate->client->first_name . ' ' . $estimate->client->last_name }}
                    </h2>
                    <p class="address text-left spacing-0 mt-4">
                        {{ $estimate->billingAddress?->address1 }}
                    </p>
                    <p class="zip_code text-left spacing-0 mt-4">{{ $estimate->billingAddress?->zip }}</p>
                </th>
                <th class="float-left spacing-0 vertical-top" style="width: 40%">
                    <p class="placeholder-text text-left spacing-0 mt-4">
                        Job site
                    </p>
                    <p class="address text-left spacing-0 mt-4">
                        {{ $estimate->propertyAddress?->address1 }}

                    </p>
                    <p class="zip_code text-left spacing-0 mt-4">{{ $estimate->propertyAddress?->zip }}</p>
                </th>
            </tr>
        </table>
    </div>

    <div style="clear: both"></div>

    <div class="w-100 mt-28">
        <table class="table w-100">
            <tr>
                @if($estimate->property_address_property_name)
                <th class="float-left spacing-0 vertical-top" style="width: 60%">
                    <p class="placeholder-text text-left spacing-0">
                        Proptery Name
                    </p>
                    <h2 class="title_name text-left spacing-0 mt-4">
                        {{ $estimate->property_address_property_name }}
                    </h2>
                </th>
                @endif
                @if($estimate->client?->company_name)
                    <th class="float-left spacing-0 vertical-top" style="width: 40%">
                        <p class="placeholder-text text-left spacing-0 mt-4">
                            Company Name
                        </p>
                        <p class="address text-left spacing-0 mt-4">
                            {{ $estimate->client?->company_name }}

                        </p>
                    </th>
                @endif
            </tr>
        </table>
    </div>

    <div style="clear: both"></div>
    @php
        $totalPayable = 0;
    @endphp
    <div class="w-100 mt-16">
        <table class="table w-100">
            <tr>
                <td class="w-100 spacing-0 vertical-top">
                    <p class="placeholder-text text-left spacing-0">
                        Project name
                    </p>
                    <h2 class="title_name text-left spacing-0 mt-4">
                        {{ $estimate->project_name }}
                    </h2>
                    <p class="address text-left spacing-0 mt-4">
                        Lorem ipsum is a placeholder text commonly used to
                        demonstrate the visual form of a document or a
                        typeface without relying on meaningful content.
                    </p>
                </td>
            </tr>
        </table>
    </div>

    <div style="clear: both"></div>

    <div class="w-100 mt-49">
        <table class="table w-100">
            <tr>
                <td class="w-100 spacing-0 vertical-top">
                    <p class="main_heading text-center spacing-0">
                        SCOPE OF WORK
                    </p>
                </td>
            </tr>
            <tr>
                <td class="w-100 spacing-0 vertical-top">
                    <p class="address text-center spacing-0 mt-4">
                        @if(isset($notes))
                            {!! $notes !!}
                        {{-- @else
                            This is note that add during esstimation ipsum is a
                            placeholder text commonly used to demonstrate the
                            visual form of a document or a typeface without
                            relying on meaningful content. --}}
                        @endif
                    </p>
                </td>
            </tr>
        </table>
    </div>

    <div style="clear: both"></div>

    <div class="panel mt-24">

        @php
           if(isset($organization_id)){
               $sales_tax=getSaleTax($organization_id);
           }else{
            $sales_tax=getSaleTax();
           }
        @endphp

        <div class="table_wrapper mt-4">
            <table class="table custom_datatable display" style="width: 100%" cellpadding="0" cellspacing="0">
                <thead>
                    <tr>
                        <th class="text-left">Items/Description</th>
                        <th class="text-left">UoM</th>
                        <th class="text-left">Quantity</th>
                        <th class="text-left">Unit Price</th>
                        <th class="text-left">Total Price</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($estimateMaterial as $item)
                        <tr>
                            <td>{{ $item->equipment->name }}</td>
                            <td>{{ $item->uom ?? $item->equipment->uom }}</td>
                            <td>{{ $item->quantity }}</td>

                            <td>${{ ($item->cost ?? $item->equipment->cost) + ($item->gross_margin / 100) * ($item->cost ?? $item->equipment->cost) ?? 0 }}
                            </td>
                            <td>${{ $item->total_cost + ($item->gross_margin / 100) * $item->total_cost ?? 0 }}
                            </td>

                        </tr>
                        @php
                            $totalPayable += $item->total_cost + ($item->gross_margin / 100) * $item->total_cost;
                        @endphp
                    @empty
                    @endforelse
                    @forelse($estimateLabor as $item)
                        <tr>
                            <td>{{ $item->labor?->name }}</td>
                            <td>{{ $item->labor?->uom }}</td>
                            <td>{{ $item->quantity }}</td>
                            {{-- <td>${{ $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost }}
                            </td>
                            <td>${{ $item->total_cost + ($item->gross_margin / 100) * $item->total_cost }}
                            </td> --}}
                            @if ($laborBurden)
                                <td>${{ $item->unit_cost + ($laborBurden / 100) * $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ?? 0 }}
                                </td>
                                <td>${{ $item->total_cost + ($laborBurden / 100) * $item->total_cost + ($item->gross_margin / 100) * $item->total_cost ?? 0 }}
                                </td>
                            @else
                                <td>${{ $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ?? 0 }}
                                </td>
                                <td>${{ $item->total_cost + ($item->gross_margin / 100) * $item->total_cost ?? 0 }}
                                </td>
                            @endif


                        </tr>
                        @php
                            if ($laborBurden) {
                                $totalPayable += $item->total_cost + ($laborBurden / 100) * $item->total_cost + ($item->gross_margin / 100) * $item->total_cost;
                            } else {
                                $totalPayable += $item->total_cost + ($item->gross_margin / 100) * $item->total_cost;
                            }
                        @endphp

                    @empty
                    @endforelse
                    @forelse ($estimateOtherCost as $item)
                        <tr>
                            <td>{{ $item->otherCost?->name }}</td>
                            <td>{{ $item->uom }}</td>
                            <td>{{ $item->quantity }}</td>
                            <td>${{ $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ?? 0 }}
                            </td>
                            <td>${{ $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity) ?? 0 }}
                            </td>
                        </tr>
                        @php
                            $totalPayable += $item->quantity * $item->unit_cost + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost);
                        @endphp
                    @empty
                    @endforelse
                    @forelse ($estimateSubContractor as $item)
                        <tr>
                            <td>{{ $item->name }}</td>
                            <td>{{ $item->uom }}</td>
                            <td>{{ $item->quantity }}</td>
                            <td>${{ $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ?? 0 }}
                            </td>
                            <td>${{ $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity) ?? 0 }}

                        </tr>
                        @php
                            $totalPayable += $item->quantity * $item->unit_cost + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost);
                        @endphp
                    @empty
                    @endforelse

                    @forelse ($estimateHardMaterial as $item)
                        <tr>
                            <td>{{ $item->material?->name }}</td>
                            <td>{{ $item->uom }}</td>
                            <td>{{ $item->quantity }}</td>
                            {{-- <td>${{ $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost }}
                            </td>
                            <td>${{ $item->quantity * $item->unit_cost + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost) }}
                            </td> --}}
                            <td>${{ $item->unit_cost + ($sales_tax / 100) * $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ?? 0 }}
                            </td>
                            <td>${{ $item->quantity * $item->unit_cost + ($sales_tax / 100) * ($item->quantity * $item->unit_cost) + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost) ?? 0 }}
                            </td>
                        </tr>
                        @php
                            $totalPayable += $item->quantity * $item->unit_cost + ($sales_tax / 100) * ($item->quantity * $item->unit_cost) + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost);
                            // $totalPayable += $item->quantity * $item->unit_cost + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost);
                        @endphp
                    @empty
                    @endforelse
                    @forelse ($estimatePlantMaterial as $item)
                        <tr>
                            <td>{{ $item->material?->name }}</td>
                            <td>{{ $item->uom }}</td>
                            <td>{{ $item->quantity }}</td>
                            {{-- <td>${{ $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost }} --}}
                            {{-- </td>
                            <td>${{ $item->quantity * $item->unit_cost + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost) }}
                            </td> --}}
                            <td>${{ $item->unit_cost + ($sales_tax / 100) * $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ?? 0 }}
                            </td>
                            <td>${{ $item->quantity * $item->unit_cost + ($sales_tax / 100) * ($item->quantity * $item->unit_cost) + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost) ?? 0 }}
                            </td>
                        </tr>
                        @php
                            $totalPayable += $item->quantity * $item->unit_cost + ($sales_tax / 100) * ($item->quantity * $item->unit_cost) + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost);

                            // $totalPayable += $item->quantity * $item->unit_cost + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost);

                        @endphp
                    @empty
                    @endforelse

                </tbody>
            </table>
        </div>
    </div>

    {{-- <div class="panel mt-16 page-break">
        <h2 class="main_heading spacing-0 leading-16 mb-16">Equipment</h2>
        <hr class="vertical_line mb-12" />
        <div class="table_wrapper mt-4">
            <table class="table custom_datatable display" style="width: 100%" cellpadding="0" cellspacing="0">
                <thead>
                    <tr>
                        <th class="text-left">Description</th>
                        <th class="text-left">UoM</th>
                        <th class="text-left">Quantity</th>
                        <th class="text-left">Gross Margin</th>
                        <th class="text-left">Unit Cost</th>
                        <th class="text-left">Total Price</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Skid Steer Brickman, per hr</td>
                        <td>Hours</td>
                        <td>12</td>
                        <td>50%</td>
                        <td>$34</td>
                        <td>$234</td>
                    </tr>
                    <tr>
                        <td>Skid Steer Brickman, per hr</td>
                        <td>Hours</td>
                        <td>12</td>
                        <td>50%</td>
                        <td>$34</td>
                        <td>$234</td>
                    </tr>
                    <tr>
                        <td>Skid Steer Brickman, per hr</td>
                        <td>Hours</td>
                        <td>12</td>
                        <td>50%</td>
                        <td>$34</td>
                        <td>$234</td>
                    </tr>
                    <tr>
                        <td>Skid Steer Brickman, per hr</td>
                        <td>Hours</td>
                        <td>12</td>
                        <td>50%</td>
                        <td>$34</td>
                        <td>$234</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div> --}}

    <div class="total_payable w-100 text-right mt-24">
        <span class="title vertical-middle">Total Payable: </span>
        <span class="value vertical-middle"> $ {{ $totalPayable }}</span>
    </div>

    <div class="approval_title w-100 mb-24">Client Approval</div>
    <div class="approval_items w-100">
        <div class="sign float-left" style="width: 170px">
            <div class="signature mb-8"></div>
            <div class="sign_title">Signature</div>
        </div>
        <div class="sign float-left" style="width: 110px">
            <div class="signature mb-8"></div>
            <div class="sign_title">Date</div>
        </div>
    </div>
    <br />
    <div class="approval_title w-100 mb-24 mt-32">
        Company ( {{ $company->company_name }} ) Approval
    </div>
    <div class="approval_items w-100 mb-24">
        <div class="sign float-left" style="width: 170px">
            <div class="signature mb-8"></div>
            <div class="sign_title">Signature</div>
        </div>
        <div class="sign float-left" style="width: 110px">
            <div class="signature mb-8"></div>
            <div class="sign_title">Date</div>
        </div>
    </div>
    <br /><br />
    <div class="bottom_bar mt-24"></div>
</body>

</html>
