<script>
    $(document).ready(function(e) {
        monthlyTargetRevenue();
        ActualVsTargetChartValue();
        revenueTargetPercentage();

        $('.targetTable').on('change', function(e) {
            monthlyTargetRevenue();
        })

        $('.targetPercentageSelector').on('change', function(e) {
            revenueTargetPercentage();
        })
    })

    function monthlyTargetRevenue() {
        $year = $('.targetTable').val();
        var data = {
            'year': $year
        }
        $.ajax({
            type: 'GET',
            url: "{{ route(getRouteAlias() . '.financeTargets') }}",
            data: data,
            success: function(response) {
                $('#monthsTargetTable').html(response.html);
            },
            error: function(xhr, status, error) {
                console.error(error);
            }
        });
    }


    function revenueTargetPercentage() {
        $year = $('.targetPercentageSelector').val();
        var data = {
            'year': $year
        }
        $.ajax({
            type: 'GET',
            url: "{{ route(getRouteAlias() . '.revenueTargetPercentage') }}",
            data: data,
            success: function(response) {
                $('.collectedRevenue').text('$' + response.totalRevenue);
                var TargetDifference = parseFloat(response.totalTarget.replace(/,/g, '')) - parseFloat(
                    response.totalRevenue.replace(/,/g, ''));
                if (TargetDifference.toFixed(0) > 0) {
                    $('.totalTarget').text('$' + TargetDifference.toFixed(0));
                } else {
                    $('.totalTarget').text('$0');

                }
                $('.TargetCompletedPercentageStatus').val(response.TargetCompletedPercentage);
                revenueTargetPercentageChart();
            },
            error: function(xhr, status, error) {
                console.error(error);
            }
        });
    }

    var doughnutChart;

    function revenueTargetPercentageChart() {
        let achievedPercentage = $('.TargetCompletedPercentageStatus').val().replace('%', '');
        if (achievedPercentage >= 100) {
            toDoPercentage = 0;
        } else {
            toDoPercentage = 100 - achievedPercentage;
        }
        if (doughnutChart) {
            doughnutChart.destroy(); // Destroy the existing waveform chart instance if it exists
        }
        var dughnutData = [achievedPercentage, toDoPercentage];

        doughnutChart = new Chart("dughnutChart", {
            type: 'doughnut',
            data: {
                labels: ['Completed', 'Remaining'],
                datasets: [{
                    label: 'Target Status',
                    data: dughnutData,
                    backgroundColor: [
                        // '#3459AB',
                        // '#FE9B6A',
                        '#0074D9',
                        '#2ECC40',
                    ],
                    hoverOffset: 4,
                    borderRadius: 0,
                    borderWidth: 0,
                    spacing: 0,
                }]
            },
            options: {
                aspectRatio: 1.1,
                plugins: {
                    legend: {
                        display: false,
                    },
                },
                cutout: '80%',
            },

        });

        document.getElementById("boxbg").style.backgroundColor = doughnutChart.data.datasets[0].backgroundColor[
            0];
        document.getElementById("boxbg2").style.backgroundColor = doughnutChart.data.datasets[0]
            .backgroundColor[1];



        document.getElementById("underView").innerText = doughnutChart.data.labels[0];
        document.getElementById("pendings").innerText = doughnutChart.data.labels[1];

        document.getElementById("dughtnValue").innerText = doughnutChart.data.datasets[0].data[0] + "%";
        document.getElementById("dughtnValue").style.color = doughnutChart.data.datasets[0].backgroundColor[0];


        function toggleDoughnut(value) {
            if (dughnutData[value] !== null) {
                dughnutData[value] = null;
                if (value === 0) {
                    dughnutData[value] = 25;
                } else {
                    dughnutData[value] = 75;
                }
            }

            // Update the chart to reflect the changes
            doughnutChart.data.datasets[0].data = dughnutData;
            doughnutChart.update();
        }
    }




    //CostSumamrY fORM

    $('.costSummaryForm').on('submit', function(e) {
        e.preventDefault();
        let formData = new FormData(this);
        $.ajax({
            type: 'POST',
            url: "{{ route(getRouteAlias() . '.costSummary') }}",
            data: formData,
            dataType: "json",
            encode: true,
            processData: false,
            contentType: false,
            cache: false,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            success: function(response) {
                $('.forcastedGpSum').html(response.forcasted_gp.toFixed(2)).prepend('$');
                $('.forcastedGpSumPercentage').html(response.forcastedGpSumPercentage.toFixed(0))
                    .append('%');
                toastr.success('Cost summary updated successfully');
            },
            error: function(xhr, status, error) {
                console.error(error);
            }
        });
    })


    //CostSummaryNotes
    $('.addCostSummaryNoteBtn').on('click', function(e) {
        $('.costSummaryId').val($(this).data('id'));
        let id = $(this).data('id');
        var url = '{{ URL::route(getRouteAlias() . '.costSummaryViewNote', ':id') }}',
            url = url.replace(':id', id);
        $.ajax({
            url: url,
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            processData: false,
            contentType: false,
            success: function(response) {
                $('.error-text').remove();
                $('.CostSummaryNotes').val(response);
                $('#ViewCostSummaryNoteModal').modal('show');
            },
            error: function(request) {

            }
        });
    })



    $('#costSummaryNote').on('submit', function(e) {
        e.preventDefault();

        var formData = new FormData(this);

        $.ajax({
            url: "{{ route('organization.costSummaryNote') }}",
            type: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('.error-text').remove();
                $('#ViewCostSummaryNoteModal').modal('hide');
                toastr.success(response.message);
            },
            error: function(request) {
                $('.error-text').remove();
                let errorResponse = JSON.parse(request.responseText);
                $.each(errorResponse.errors, function(field_name, error) {
                    $(document)
                        .find("#costSummaryNote" + " [name=" + field_name + "]")
                        .after(
                            '<span class="text-strong text-danger error-text span2-3">' +
                            error +
                            "</span>"
                        );
                });
            }
        });
    });




    $('#actual-vs-target-year').on('change', function(e) {
        ActualVsTargetChartValue();
    })



    function ActualVsTargetChartValue() {
        $year = $('#actual-vs-target-year').val();
        var data = {
            'year': $year
        }
        $.ajax({
            type: 'GET',
            url: "{{ route(getRouteAlias() . '.revenue.actualVsTarget') }}",
            data: data,
            success: function(response) {
                ActualVsTargetChart(response.revenue_array, response.target_array)
            },
            error: function(xhr, status, error) {
                console.error(error);
            }
        });
    }
    //Actual Vs Target Chart
    function ActualVsTargetChart($one, $two) {
        var chartRisk = Highcharts.chart('riskChart', {

            credits: {
                enabled: false
            },
            legend: {
                enabled: false
            },
            chart: {
                type: 'column',
                style: {
                    borderRadius: '6px',
                    background: '#FFFFFF',
                    boxShadow: '0px 4px 20px rgba(13, 10, 44, 0.08)',
                    padidng: '16px'
                },
                height: 344,
                events: {
                    load: function() {
                        if (this.chartHeight < 300) {
                            this.setSize(undefined, 300);
                        }
                    }
                }

            },
            title: {
                enabled: false,
                text: '',
            },
            xAxis: {
                categories: ['Jan', 'Feb', 'Mar', 'April', 'May', 'June', 'July', 'Aug', 'Sep', 'Oct', 'Nov',
                    'Dec'
                ],

                labels: {
                    style: {
                        fontFamily: 'Poppins',
                        fontStyle: 'normal',
                        fontSize: '14px',
                        lineHeight: '16px',
                        fontWeight: '400',
                        color: 'rgba(27, 35, 55, 0.8)',
                    }
                },
                min: 0,
                max: 11,
                title: {
                    text: '',

                },

                // crosshair: true
            },
            yAxis: {
                // categories: [70000, 80000, 100000, 120000, 200000, 250000],
                // tickPositions: [100, 20000, 40000, 60000, 80000, 100000],
                tickPositioner: function() {
                    // Check if all values are zero
                    const allZeroes = this.dataMax === 0 && this.dataMin === 0;

                    if (allZeroes) {
                        // Custom tick positions for all zeroes case
                        return [1, 20000, 40000, 60000, 80000, 100000];
                    } else {
                        var positions = [],
                            tick = Math.floor(this.dataMin),
                            increment = Math.ceil((this.dataMax - this.dataMin) / 5);
                        for (tick; tick - increment <= this.dataMax; tick += increment) {
                            positions.push('$' + tick);
                        }
                        return positions;
                    }
                },
                labels: {
                    formatter: function() {
                        if (this.value >= 1000) {
                            return ('$' + this.value / 1000) + 'k';
                        } else {
                            return '$' + this.value;
                        }
                    },
                    style: {
                        fontFamily: 'Poppins',
                        fontStyle: 'normal',
                        fontSize: '14px',
                        lineHeight: '18px',
                        fontWeight: '400',
                        color: '#615E83',
                    }
                },
                // beginAtZero: true,
                // startOnTick: true,
                // reversed: true,


                // tickInterval: 20,
                title: {
                    text: '',

                },

            },
            tooltip: {
                headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                    '<td style="padding:0"><b>$ {point.y:.2f}</b></td></tr>', // Added space after "$"
                footerFormat: '</table>',
                shared: true,
                useHTML: true
            },

            plotOptions: {

                column: {
                    groupPadding: 0.2,
                    borderWidth: 1,
                    borderRadius: 5,
                    borderColor: 'white',
                }
            },
            series: [{
                    name: 'Total Sales',
                    data: $one,
                    pointWidth: 12,
                    // color: '#036',
                    color: '#0074D9',

                },
                {
                    name: 'Total Target',
                    data: $two,
                    pointWidth: 12,
                    // color: '#FE9B6A',
                    color: '#2ECC40',

                },


            ]
        });


        document.getElementById("low2").style.backgroundColor = chartRisk.series[0].color;
        document.getElementById("moderate2").style.backgroundColor = chartRisk.series[1].color;

        document.getElementById("one1").innerText = chartRisk.series[0].name;
        document.getElementById("two2").innerText = chartRisk.series[1].name;

        function toggleDataRisk(value) {
            var seriesed2 = chartRisk.series[value]; // Get the series object by index

            // console.log(seriesed);
            if (seriesed2) { // Check that the seriesed object is not undefined
                if (seriesed2.visible) {
                    seriesed2.hide();
                } else {
                    seriesed2.show();
                }
            } else {
                console.log('Invalid series index:', value);
            }
        }

    }


    $(document).ready(function(e) {
        $('.cost_summary_month_filter').on('change', function(e) {
            $('.clearFiltersBtn').removeClass('d-none');
            $value = $(this).val();
            $yearValue = $('.cost_summary_year_filter').val();
            console.log($value, $yearValue);

            var data = {
                'month': $value,
                'year': $yearValue
            }
            $.ajax({
                type: 'GET',
                url: "{{ route(getRouteAlias() . '.costSummaryFilter') }}",
                data: data,
                success: function(response) {
                    $('.costSummaryTable').html(response.table);
                    $('.forcastedGpSum').html(response.forcasted_gp).prepend('$');
                    $('.forcastedGpSumPercentage').html(response.forcasted_gp_per).append(
                        '%');
                    $('.forcasted_revenue_value').html(response.forcasted_revenue.toFixed(
                        2)).prepend(
                        '$');
                    $('.hr_value').html(response.Hr);
                },
                error: function(xhr, status, error) {
                    console.error(error);
                }
            });
        })

        $('.cost_summary_year_filter').on('change', function(e) {
            $('.clearFiltersBtn').removeClass('d-none');
            $value = $(this).val();
            $monthValue = $('.cost_summary_month_filter').val();
            console.log($value, $monthValue);
            var data = {
                'year': $value,
                'month': $monthValue
            }
            $.ajax({
                type: 'GET',
                url: "{{ route(getRouteAlias() . '.costSummaryFilter') }}",
                data: data,
                success: function(response) {
                    $('.costSummaryTable').html(response.table);
                    $('.forcastedGpSum').html(response.forcasted_gp).prepend('$');
                    $('.forcastedGpSumPercentage').html(response.forcasted_gp_per).append(
                        '%');
                    $('.forcasted_revenue_value').html(response.forcasted_revenue.toFixed(
                        2)).prepend(
                        '$');
                    $('.hr_value').html(response.Hr);
                },
                error: function(xhr, status, error) {
                    console.error(error);
                }
            });
        })
    })

    $(document).ready(function(e) {
        $('.clearFiltersBtn').click(function(e) {
            var data = {
                'year': '',
                'month': ''
            }
            $.ajax({
                type: 'GET',
                url: "{{ route(getRouteAlias() . '.costSummaryFilter') }}",
                data: data,
                success: function(response) {
                    $(".basic-single-select").val(null).trigger("change")
                    $('.costSummaryTable').html(response.table);
                    $('.forcastedGpSum').html(response.forcasted_gp).prepend('$');
                    $('.forcastedGpSumPercentage').html(response.forcasted_gp_per).append(
                        '%');
                    $('.forcasted_revenue_value').html(response.forcasted_revenue.toFixed(
                        2)).prepend(
                        '$');
                    $('.hr_value').html(response.Hr);
                },
                error: function(xhr, status, error) {
                    console.error(error);
                }
            });
        })
    })
</script>
