{{-- @foreach ($monthlySums as $item)
    <tr>
        <td>{{ $item['month'] }}</td>
        <td>${{ number_format($item['revenue'], 0) }}</td>
        <td>${{ $item['target'] }}</td>
        <td>
            {{ $item['target'] == 0 ? '---' : number_format(($item['revenue'] / $item['target']) * 100, 0) . '%' }}
        </td>

    </tr>
@endforeach

<tr>
    <td><strong>Total</strong></td>
    <td><strong>${{ number_format($monthlySums->sum('revenue'), 0) }}</strong></td>
    <td><strong>${{ number_format($monthlySums->sum('target'), 0) }}</strong></td>
    <td><strong
            class="targetCompletePercentage">{{ $monthlySums->sum('target') == '0' ? '0%' : number_format(($monthlySums->sum('revenue') / $monthlySums->sum('target')) * 100, 0) . '%' }}</strong>
    </td>
</tr> --}}



@foreach ($monthlySums as $item)
    <tr>
        <td>{{ $item['month'] }}</td>
        <td>${{ number_format($item['revenue'], 0) }}</td>
        <td>
            @if ($item['target'] === null)
                ---
            @else
                ${{ number_format($item['target'], 0) }}
            @endif
        </td>
        <td>
            @if ($item['target'] === null)
                ---
            @else
                {{ number_format(($item['revenue'] / ($item['target'] == 0 ? 1 : $item['target'])) * 100, 0) . '%' }}
            @endif
        </td>
    </tr>
@endforeach

<tr>
    <td><strong>Total</strong></td>
    <td><strong>${{ number_format($monthlySums->sum('revenue'), 0) }}</strong></td>
    <td><strong>
            @if ($monthlySums->sum('target') === null)
                ---
            @else
                ${{ number_format($monthlySums->sum('target'), 0) }}
            @endif
        </strong></td>
    <td><strong class="targetCompletePercentage">
            @if ($monthlySums->sum('target') === null || $monthlySums->sum('target') == 0)
                0%
            @else
                {{ number_format(($monthlySums->sum('revenue') / $monthlySums->sum('target')) * 100, 0) . '%' }}
            @endif
        </strong></td>
</tr>
