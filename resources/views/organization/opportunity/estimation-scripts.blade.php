{{--@push('scripts')--}}
<!-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> -->
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
{{--        <script src="https://cdn.tiny.cloud/1/8gngu979gm8n88nvs8ktqf0ma89m85gp4s6izjbx4l9tfap4/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>--}}
<script src="{{ asset('assets/tinymce/js/tinymce/tinymce.min.js') }}"></script>
<script>
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    tinymce.init({
        selector: '#textEditor',
        plugins: 'lists',
        toolbar: 'bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
        height: 300,
        menubar: false,
        branding: false,
        toolbar_mode: 'wrap',
        content_style: "ul { list-style-type: disc; } ol { list-style-type: decimal; }"
    });

    // code for drag and drop
    document.addEventListener("DOMContentLoaded", function () {
        const tableBody = document.querySelector('#sortable-table-body');

        // Function to apply height to rows without id or class
        function applyHeightToRows() {
            tableBody.querySelectorAll('tr').forEach((row) => {
                if (!row.hasAttribute('id') && !row.hasAttribute('class')) {
                    // row.style.setProperty('height', '40px', 'important');
                }
            });
        }

        // Apply height to rows initially
        applyHeightToRows();

        // Set up MutationObserver to watch for added rows
        if (tableBody) {
            const observer = new MutationObserver(() => {
                applyHeightToRows(); // Apply height when new rows are added
            });

            // Observe changes (child additions/removals) within the table body
            observer.observe(tableBody, {childList: true, subtree: true});
        }
    });

    document.addEventListener("DOMContentLoaded", function () {
        const container = document.querySelector("table"); // Parent container (adjust this selector as per your table)

        container.addEventListener("change", function (event) {
            const target = event.target;

            // Check if the changed element is a checkbox with the desired class
            if (target.classList.contains("dynamic-checkbox")) {
                // Get all checkboxes
                const checkboxes = container.querySelectorAll(".dynamic-checkbox");

                // Uncheck all other checkboxes
                checkboxes.forEach((box) => {
                    if (box !== target) {
                        box.checked = false;
                        box.classList.remove("checked-class");
                        box.removeAttribute("data-checked-id");
                    }
                });

                // Add class and data-id to the checked checkbox
                if (target.checked) {
                    target.classList.add("checked-class");
                    target.setAttribute("data-checked-id", target.dataset.id);
                } else {
                    target.classList.remove("checked-class");
                    target.removeAttribute("data-checked-id");
                }
            }
        });
    });


    document.getElementById('doneBtnDefinescope').addEventListener('click', function () {
        const finalTitleText = $('#scope_title').val();
        // const finalDescriptionText = document.getElementById('addNoteSummer').value;
        const finalDescriptionText = tinymce.get('textEditor').getContent();


        originalTitleText = finalTitleText;
        originalDescriptionText = finalDescriptionText;
        // originalPreviewImageSrc = finalPreviewImageSrc;

        let formData = new FormData();

        // console.info(fileInput.files);
        // Append other form data
        formData.append('cov_title', originalTitleText);
        formData.append('sub_tit', originalDescriptionText);

        formData.append('project_checked', 'false');
        var opportunity_id = {{$opportunityId}};
        formData.append('opportunity_id', opportunity_id);
        console.log(formData);
        // Check the checkbox status


        var url = "{{ URL::route(getRouteAlias() . '.storeDefaultSettingsScope') }}";
        // Send the data to the backend using AJAX
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                console.info('Data sent successfully:', response);
                $('#definescope').modal('hide');
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.info('Error sending data:', textStatus, errorThrown);
            }
        });


    });

    document.addEventListener("DOMContentLoaded", function () {
        const checkboxes = document.querySelectorAll(".accordion-checkbox");

        checkboxes.forEach((checkbox) => {
            checkbox.addEventListener("change", function () {
                if (this.checked) {
                    checkboxes.forEach((cb) => {
                        if (cb !== this) {
                            cb.checked = false; // Close other tabs
                        }
                    });
                }
            });
        });
    });

    $(document).on('click','.add-scope-btn-modal',function () {
        /*$('#scope_title').val('');
        tinymce.get('textEditor').setContent('');*/
    });

    function searchItems() {
        var search = $('.serchinputss').val();
        console.info(search);
        if (search == null) {
            $('#accordion-container').css('display', 'none');
        }
        $.ajax({
            type: "GET",
            url: "{{ route('organization.estimation.search-item') }}",
            data: {search: search},
            success: function (response) {
                $('#accordion-container').css('display', 'none');
                if (search == null || search == '') {
                    $('#accordion-container').css('display', 'block');
                    $('.itemsContainerss').css('display', 'none');
                } else {
                    $('#accordion-container').css('display', 'none');
                    $('.itemsContainerss').css('display', 'block');

                }
                // Container to hold all items
                let allItems = '';

// Loop through each category
                $.each(response, function (category, items) {
                    // Add category title
                    // allItems += `<li class="category-title">${capitalizeFirstLetter(category)}</li>`;
// console.info(category);
                    // Loop through items within the category
                    $.each(items, function (index, item) {

                        let isFavorite = item.is_favorite == 0
                            ? `<svg width="16" id="nofav-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, '${category}')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>`
                            : `<svg width="16" onclick="deleteItemToFavourite(${item.id}, '${category}')" id="yesfav-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>`;

                        let functionName = '';
                        switch (category) {
                            case 'equipment':
                                functionName = 'addItemTo';
                                break;
                            case 'labors':
                                functionName = 'addItemLabor';
                                break;
                            case 'hard_materials':
                                functionName = 'addItemhardM';
                                break;
                            case 'plant_materials':
                                functionName = 'addItemhardP';
                                break;
                            case 'other_costs':
                                functionName = 'addItemCost';
                                break;
                            case 'sub_contractors':
                                functionName = 'addItemContractor';
                                break;
                            default:
                                console.error(`Unknown category: ${category}`);
                        }

                        // Append item details
                        allItems += `
            <li class="menu-accordian mt-4">
                <div class="row">
                    <div class="col-1" style="margin-top: 2px !important;">${isFavorite}</div>
                    <div class="col-7 text-left">
                        <span>${item.name}</span>
                    </div>
                    <div id="${item.id}" class="col-3" style="display: flex; justify-content: end">
                        <button id="${category}plusbtn-${item.id}" onclick="${functionName}(${item.id})" style="width: 24px; height: 24px; border-radius: 50%; text-align: center; margin-left: auto; color: white; border: none; font-size: 18px; background-color: #2FCC40;">
                            <b style="color: white; font-size: 17px;">+</b>
                        </button>
                    </div>
                </div>
            </li>`;
                    });
                });

// Append all items to the container
                $('#itemsContainer').empty().append(allItems);
            }
        });


    }

    function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1).replace('_', ' ');
    }

    function finalsellingP() {
        let totalPrice = parseFloat($('#forceselling').val()) || 0;
        let totalCostText = $('#totalallcount').text().replace('$', '');
        let totalCost = parseFloat(totalCostText) || 0;

        if (totalPrice == 0) {
            $('#forcegm').prop('readonly', false).removeClass('disabledbtn');
            $('#forcefinalgm').val('');
            $('#forcefinalselling').val('');
        } else {
            $('#forcegm').prop('readonly', true).addClass('disabledbtn');
            let gm = ((totalPrice - totalCost) / totalPrice) * 100;
            $('#forcefinalgm').val(gm.toFixed(2));
            $('#forcefinalselling').val(totalPrice.toFixed(2));
        }

        setTimeout(() => {
            changeGrossMarginForAllInputFields();
        }, 100);
    }

    function finalsellingG() {
        let gm = parseFloat($('#forcegm').val()) || 0;
        let totalCostText = $('#totalallcount').text().replace('$', '').trim();
        let totalCost = parseFloat(totalCostText) || 0;

        if (gm === 0) {
            $('#forceselling').prop('readonly', false).removeClass('disabledbtn');
            $('#forcefinalgm').val('');
            $('#forcefinalselling').val('');
        } else {
            $('#forceselling').prop('readonly', true).addClass('disabledbtn');

            let totalPrice = totalCost / (1 - gm / 100);
            $('#forcefinalgm').val(gm.toFixed(2));
            $('#forcefinalselling').val(totalPrice.toFixed(2));
        }

        setTimeout(() => {
            changeGrossMarginForAllInputFields();
        }, 100);
    }

    function changeGrossMarginForAllInputFields() {
        const newGrossMargin = $('#forcefinalgm').val() ?? 0;
        const newSellingPrice = $('#forcefinalselling').val() ?? 0;
        const addedSellingPrice = $('#forceselling').val() ?? 0;
        const addedMargin = $('#forcegm').val() ?? 0;
        $.ajax({
            method: 'POST',
            url: '{{ route('organization.update-estimate-item-margin') }}',
            data: {
                opportunity_id: '{{ $opportunityId }}',
                margin: newGrossMargin,
                new_selling_price: newSellingPrice,
                add_selling_price: addedSellingPrice,
                added_margin: addedMargin,
            },
            success: function (response) {
                if(response) {
                    $('#estimateItemsTable').empty().html(response.html);
                    $('#totalSectionEstimateItems').empty().html(response?.estimateTotal);
                }
            },
            error: function (error) {
                console.log('error:',error);
            }
        })

        /*$('.margin-default-value').each(function () {
            const $label = $(this);
            $label.text(newGrossMargin);

            const id = $label.attr('data-id');
            if (!id) {
                console.warn('Missing data-id on', $label);
                return;
            }

            // Match ALL elements with class `gross-margin-input-[id]`
            const selector = `.gross-margin-input-${id}`;
            const $inputs = $(selector);

            if ($inputs.length === 0) {
                console.warn(`No inputs found for selector: ${selector}`);
            }

            $inputs.each(function () {
                $(this).val(newGrossMargin).trigger('input');
                console.log(`Triggered input on`, this);
            });
        });*/
    }

</script>

{{--<script>

    // Target element (totalcountlabor)
    const targetElementLaborBurden = document.getElementById('labortotalprice');

    // MutationObserver ka instance create karna
    const observerLaborBurden = new MutationObserver(function (mutationsList, observerLaborBurden) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var vlLaborBurden = $('#totalcountlaborPrice').text();  // Get the text including $
                vlLaborBurden = vlLaborBurden.replace('$', '');  // Remove the $ sign
                vlLaborBurden = parseFloat(vlLaborBurden);  // Convert the string to a floating point number

                var sleLaborBurden = $('#laborburdenval').val();  // Get the sales tax value
                var mlLaborBurden = (sleLaborBurden / 100);  // Calculate the percentage
                var vgLaborBurden = vlLaborBurden * mlLaborBurden;

                $('#totalcountburdenPrice').text('$' + vgLaborBurden.toFixed(2));  // Update total labor burden sales tax
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    observerLaborBurden.observe(targetElementLaborBurden, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        targetElementLaborBurden.textContent = "${{$totalPriceSumlabor}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountlabor)
    const targetElement = document.getElementById('totalcountmaterialPrice');

    // MutationObserver ka instance create karna
    const observer = new MutationObserver(function (mutationsList, observer) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var vl = $('#totalcountmaterialPrice').text();  // Get the text including $
                vl = vl.replace('$', '');  // Remove the $ sign
                vl = parseFloat(vl);  // Convert the string to a floating point number

                var sle = $('#salestaxuser').val();  // Get the sales tax value
                var ml = (sle / 100);  // Calculate the percentage
                var vg = vl * ml;
                $('#totalmaterialsaletexPrice').text('$' + vg.toFixed(2));
                // onTextChange(vl);
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    observer.observe(targetElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        targetElement.textContent = "${{$totalPriceSummaterial}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountmaterial)
    const totalCountEquipmentTotalElement = document.getElementById('totalcountequip');

    // MutationObserver ka instance create karna
    const equipmentTotalObserver = new MutationObserver(function (mutationsList, equipmentTotalObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var totalEquipmentTotalCost = $('#totalcountlabor').text();  // Get the text including $
                totalEquipmentTotalCost = totalEquipmentTotalCost.replace('$', '');  // Remove the $ sign
                totalEquipmentTotalCost = parseFloat(totalEquipmentTotalCost);  // Convert the string to a floating point number

                var totalEquipmentTotalCost2 = $('#totalcountequip').text();  // Get the text including $
                totalEquipmentTotalCost2 = totalEquipmentTotalCost2.replace('$', '');  // Remove the $ sign
                totalEquipmentTotalCost2 = parseFloat(totalEquipmentTotalCost2);

                var totalEquipmentTotalCost3 = $('#totalcountmaterial').text();  // Get the text including $
                totalEquipmentTotalCost3 = totalEquipmentTotalCost3.replace('$', '');  // Remove the $ sign
                totalEquipmentTotalCost3 = parseFloat(totalEquipmentTotalCost3);

                var totalEquipmentTotalCost4 = $('#totalcountother').text();  // Get the text including $
                totalEquipmentTotalCost4 = totalEquipmentTotalCost4.replace('$', '');  // Remove the $ sign
                totalEquipmentTotalCost4 = parseFloat(totalEquipmentTotalCost4);

                var totalEquipmentTotalCost5 = $('#totalSubContractorsValu').text();  // Get the text including
                totalEquipmentTotalCost5 = totalEquipmentTotalCost5.replace('$', '');  // Remove the $ sign
                totalEquipmentTotalCost5 = parseFloat(totalEquipmentTotalCost5);

                var tyt = totalEquipmentTotalCost + totalEquipmentTotalCost2 + totalEquipmentTotalCost3 + totalEquipmentTotalCost4 + totalEquipmentTotalCost5;

                $('#totalallcount').text('$' + tyt.toFixed(2));  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    equipmentTotalObserver.observe(totalCountEquipmentTotalElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalCountEquipmentTotalElement.textContent = "${{$totalCostSumequip}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountequip)
    const totalCountEquipmentTotalPriceElement = document.getElementById('totalcountequipPrice');

    // MutationObserver ka instance create karna
    const equipmentTotalPriceObserver = new MutationObserver(function (mutationsList, equipmentTotalPriceObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var totalEquipmentLaborPrice = $('#totalcountlaborPrice').text();  // Get the text including $
                totalEquipmentLaborPrice = totalEquipmentLaborPrice.replace('$', '');  // Remove the $ sign
                totalEquipmentLaborPrice = parseFloat(totalEquipmentLaborPrice);  // Convert the string to a floating point number

                var totalEquipmentPrice = $('#totalcountequipPrice').text();  // Get the text including $
                totalEquipmentPrice = totalEquipmentPrice.replace('$', '');  // Remove the $ sign
                totalEquipmentPrice = parseFloat(totalEquipmentPrice);

                var totalMaterialPrice = $('#totalcountmaterialPrice').text();  // Get the text including $
                totalMaterialPrice = totalMaterialPrice.replace('$', '');  // Remove the $ sign
                totalMaterialPrice = parseFloat(totalMaterialPrice);

                var totalOtherPrice = $('#totalcountotherPrice').text();  // Get the text including $
                totalOtherPrice = totalOtherPrice.replace('$', '');  // Remove the $ sign
                totalOtherPrice = parseFloat(totalOtherPrice);

                var totalSubContractorsPrice = $('#totalSubContractorsValuPrice').text();  // Get the text including $
                totalSubContractorsPrice = totalSubContractorsPrice.replace('$', '');  // Remove the $ sign
                totalSubContractorsPrice = parseFloat(totalSubContractorsPrice);

                var totalAllPrice = totalEquipmentLaborPrice + totalEquipmentPrice + totalMaterialPrice + totalOtherPrice + totalSubContractorsPrice;

                $('#totalPriceCount').text('$' + totalAllPrice.toFixed(2));  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    equipmentTotalPriceObserver.observe(totalCountEquipmentTotalPriceElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalCountEquipmentTotalPriceElement.textContent = "${{$totalPriceSumequip}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountmaterial)
    const totalCountLaborTotalElement = document.getElementById('totalcountlabor');

    // MutationObserver ka instance create karna
    const laborTotalObserver = new MutationObserver(function (mutationsList, laborTotalObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var totalLaborTotalCost = $('#totalcountlabor').text();  // Get the text including $
                totalLaborTotalCost = totalLaborTotalCost.replace('$', '');  // Remove the $ sign
                totalLaborTotalCost = parseFloat(totalLaborTotalCost);  // Convert the string to a floating point number

                var totalLaborTotalCost2 = $('#totalcountequip').text();  // Get the text including $
                totalLaborTotalCost2 = totalLaborTotalCost2.replace('$', '');  // Remove the $ sign
                totalLaborTotalCost2 = parseFloat(totalLaborTotalCost2);
                var totalLaborTotalCost3 = $('#totalcountmaterial').text();  // Get the text including $
                totalLaborTotalCost3 = totalLaborTotalCost3.replace('$', '');  // Remove the $ sign
                totalLaborTotalCost3 = parseFloat(totalLaborTotalCost3);

                var totalLaborTotalCost4 = $('#totalcountother').text();  // Get the text including $
                totalLaborTotalCost4 = totalLaborTotalCost4.replace('$', '');  // Remove the $ sign
                totalLaborTotalCost4 = parseFloat(totalLaborTotalCost4);

                var totalLaborTotalCost5 = $('#totalSubContractorsValu').text();  // Get the text including
                totalLaborTotalCost5 = totalLaborTotalCost5.replace('$', '');  // Remove the $ sign
                totalLaborTotalCost5 = parseFloat(totalLaborTotalCost5);


                var tyt = totalLaborTotalCost + totalLaborTotalCost2 + totalLaborTotalCost3 + totalLaborTotalCost4 + totalLaborTotalCost5;
                console.info(tyt);
                $('#totalallcount').text('$' + tyt.toFixed(2));  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    // laborTotalObserver.observe(totalCountLaborTotalElement, { childList: true });

    // // Example: Text ko dynamically change karne ka tareeqa
    // setTimeout(() => {
    //     totalCountLaborTotalElement.textContent = "${{$totalCostSumlabor}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    // }, 2000);


    // Target element (totalcountlabor)
    const totalCountLaborElement = document.getElementById('totalcountlaborPrice');

    // MutationObserver ka instance create karna
    const laborCostObserver = new MutationObserver(function (mutationsList, laborCostObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var laborCost = $('#totalcountlaborPrice').text();  // Get the text including $
                laborCost = laborCost.replace('$', '');  // Remove the $ sign
                laborCost = parseFloat(laborCost);  // Convert the string to a floating point number

                var equipCost = $('#totalcountequipPrice').text();  // Get the text including $
                equipCost = equipCost.replace('$', '');  // Remove the $ sign
                equipCost = parseFloat(equipCost);

                var materialCost = $('#totalcountmaterialPrice').text();  // Get the text including $
                materialCost = materialCost.replace('$', '');  // Remove the $ sign
                materialCost = parseFloat(materialCost);

                var otherCost = $('#totalcountotherPrice').text();  // Get the text including $
                otherCost = otherCost.replace('$', '');  // Remove the $ sign
                otherCost = parseFloat(otherCost);

                var subcontractorCost = $('#totalSubContractorsValuPrice').text();  // Get the text including $
                subcontractorCost = subcontractorCost.replace('$', '');  // Remove the $ sign
                subcontractorCost = parseFloat(subcontractorCost);

                var totalProjectCost = laborCost + equipCost + materialCost + otherCost + subcontractorCost;

                $('#totalPriceCount').text('$' + totalProjectCost.toFixed(2));  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    laborCostObserver.observe(totalCountLaborElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalCountLaborElement.textContent = "${{$totalPriceSumlabor}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Observer ko target element per observe karna shuru karna
    laborTotalObserver.observe(totalCountLaborTotalElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalCountLaborTotalElement.textContent = "${{$totalCostSumlabor}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountmaterial)
    const totalCountMaterialTotalElement = document.getElementById('totalcountmaterial');

    // MutationObserver ka instance create karna
    const materialTotalObserver = new MutationObserver(function (mutationsList, materialTotalObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var totalMaterialTotalCost = $('#totalcountlabor').text();  // Get the text including $
                totalMaterialTotalCost = totalMaterialTotalCost.replace('$', '');  // Remove the $ sign
                totalMaterialTotalCost = parseFloat(totalMaterialTotalCost);  // Convert the string to a floating point number

                var totalMaterialTotalCost2 = $('#totalcountequip').text();  // Get the text including $
                totalMaterialTotalCost2 = totalMaterialTotalCost2.replace('$', '');  // Remove the $ sign
                totalMaterialTotalCost2 = parseFloat(totalMaterialTotalCost2);

                var totalMaterialTotalCost3 = $('#totalcountmaterial').text();  // Get the text including $
                totalMaterialTotalCost3 = totalMaterialTotalCost3.replace('$', '');  // Remove the $ sign
                totalMaterialTotalCost3 = parseFloat(totalMaterialTotalCost3);

                var totalMaterialTotalCost4 = $('#totalcountother').text();  // Get the text including $
                totalMaterialTotalCost4 = totalMaterialTotalCost4.replace('$', '');  // Remove the $ sign
                totalMaterialTotalCost4 = parseFloat(totalMaterialTotalCost4);

                var totalMaterialTotalCost5 = $('#totalSubContractorsValu').text();  // Get the text including
                totalMaterialTotalCost5 = totalMaterialTotalCost5.replace('$', '');  // Remove the $ sign
                totalMaterialTotalCost5 = parseFloat(totalMaterialTotalCost5);

                var tyt = totalMaterialTotalCost + totalMaterialTotalCost2 + totalMaterialTotalCost3 + totalMaterialTotalCost4 + totalMaterialTotalCost5;

                $('#totalallcount').text('$' + tyt.toFixed(2));  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    materialTotalObserver.observe(totalCountMaterialTotalElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalCountMaterialTotalElement.textContent = "${{$totalCostSummaterial}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountlaborPrice)
    const totalLaborCostElement = document.getElementById('totalcountmaterialPrice');

    // MutationObserver ka instance create karna
    const uniqueLaborCostObserver = new MutationObserver(function (mutationsList, uniqueLaborCostObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var uniqueLaborCostPrice = $('#totalcountlaborPrice').text();  // Get the text including $
                uniqueLaborCostPrice = uniqueLaborCostPrice.replace('$', '');  // Remove the $ sign
                uniqueLaborCostPrice = parseFloat(uniqueLaborCostPrice);  // Convert the string to a floating point number

                var uniqueEquipCostPrice = $('#totalcountequipPrice').text();  // Get the text including $
                uniqueEquipCostPrice = uniqueEquipCostPrice.replace('$', '');  // Remove the $ sign
                uniqueEquipCostPrice = parseFloat(uniqueEquipCostPrice);

                var uniqueMaterialCostPrice = $('#totalcountmaterialPrice').text();  // Get the text including $
                uniqueMaterialCostPrice = uniqueMaterialCostPrice.replace('$', '');  // Remove the $ sign
                uniqueMaterialCostPrice = parseFloat(uniqueMaterialCostPrice);

                var uniqueOtherCostPrice = $('#totalcountotherPrice').text();  // Get the text including $
                uniqueOtherCostPrice = uniqueOtherCostPrice.replace('$', '');  // Remove the $ sign
                uniqueOtherCostPrice = parseFloat(uniqueOtherCostPrice);

                var uniqueSubcontractorCostPrice = $('#totalSubContractorsValuPrice').text();  // Get the text including $
                uniqueSubcontractorCostPrice = uniqueSubcontractorCostPrice.replace('$', '');  // Remove the $ sign
                uniqueSubcontractorCostPrice = parseFloat(uniqueSubcontractorCostPrice);

                var uniqueTotalProjectCost = uniqueLaborCostPrice + uniqueEquipCostPrice + uniqueMaterialCostPrice + uniqueOtherCostPrice + uniqueSubcontractorCostPrice;

                $('#totalPriceCount').text('$' + uniqueTotalProjectCost.toFixed(2));  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    uniqueLaborCostObserver.observe(totalLaborCostElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalLaborCostElement.textContent = "${{$totalPriceSummaterial}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountmaterial)
    const totalCountOtherTotalElement = document.getElementById('totalcountother');
    // MutationObserver ka instance create karna
    const otherTotalObserver = new MutationObserver(function (mutationsList, otherTotalObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var totalOtherTotalCost = $('#totalcountlabor').text();  // Get the text including $
                totalOtherTotalCost = totalOtherTotalCost.replace('$', '');  // Remove the $ sign
                totalOtherTotalCost = parseFloat(totalOtherTotalCost);  // Convert the string to a floating point number

                var totalOtherTotalCost2 = $('#totalcountequip').text();  // Get the text including $
                totalOtherTotalCost2 = totalOtherTotalCost2.replace('$', '');  // Remove the $ sign
                totalOtherTotalCost2 = parseFloat(totalOtherTotalCost2);

                var totalOtherTotalCost3 = $('#totalcountmaterial').text();  // Get the text including $
                totalOtherTotalCost3 = totalOtherTotalCost3.replace('$', '');  // Remove the $ sign
                totalOtherTotalCost3 = parseFloat(totalOtherTotalCost3);

                var totalOtherTotalCost4 = $('#totalcountother').text();  // Get the text including $
                totalOtherTotalCost4 = totalOtherTotalCost4.replace('$', '');  // Remove the $ sign
                totalOtherTotalCost4 = parseFloat(totalOtherTotalCost4);

                var totalOtherTotalCost5 = $('#totalSubContractorsValu').text();  // Get the text including
                totalOtherTotalCost5 = totalOtherTotalCost5.replace('$', '');  // Remove the $ sign
                totalOtherTotalCost5 = parseFloat(totalOtherTotalCost5);

                var tyt = totalOtherTotalCost + totalOtherTotalCost2 + totalOtherTotalCost3 + totalOtherTotalCost4 + totalOtherTotalCost5;

                $('#totalallcount').text('$' + tyt.toFixed(2));  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    otherTotalObserver.observe(totalCountOtherTotalElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalCountOtherTotalElement.textContent = "${{$totalCostSumother}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountlaborPrice)
    const totalCountLaborPriceElement = document.getElementById('totalcountotherPrice');

    // MutationObserver ka instance create karna
    const laborCostPriceObserver = new MutationObserver(function (mutationsList, laborCostPriceObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var laborCostPrice = $('#totalcountlaborPrice').text();  // Get the text including $
                laborCostPrice = laborCostPrice.replace('$', '');  // Remove the $ sign
                laborCostPrice = parseFloat(laborCostPrice);  // Convert the string to a floating point number

                var equipCostPrice = $('#totalcountequipPrice').text();  // Get the text including $
                equipCostPrice = equipCostPrice.replace('$', '');  // Remove the $ sign
                equipCostPrice = parseFloat(equipCostPrice);

                var materialCostPrice = $('#totalcountmaterialPrice').text();  // Get the text including $
                materialCostPrice = materialCostPrice.replace('$', '');  // Remove the $ sign
                materialCostPrice = parseFloat(materialCostPrice);

                var otherCostPrice = $('#totalcountotherPrice').text();  // Get the text including $
                otherCostPrice = otherCostPrice.replace('$', '');  // Remove the $ sign
                otherCostPrice = parseFloat(otherCostPrice);

                var subcontractorCostPrice = $('#totalSubContractorsValuPrice').text();  // Get the text including $
                subcontractorCostPrice = subcontractorCostPrice.replace('$', '');  // Remove the $ sign
                subcontractorCostPrice = parseFloat(subcontractorCostPrice);

                var totalProjectCostPrice = laborCostPrice + equipCostPrice + materialCostPrice + otherCostPrice + subcontractorCostPrice;

                $('#totalPriceCount').text('$' + totalProjectCostPrice.toFixed(2));  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    laborCostPriceObserver.observe(totalCountLaborPriceElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalCountLaborPriceElement.textContent = "${{$totalPriceSumother}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountmaterial)
    const totalCountSubContractorsTotalElement = document.getElementById('totalSubContractorsValu');

    // MutationObserver ka instance create karna
    const SubContractorsTotalObserver = new MutationObserver(function (mutationsList, SubContractorsTotalObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var totalSubContractorsTotalCost = $('#totalcountlabor').text();  // Get the text including $
                totalSubContractorsTotalCost = totalSubContractorsTotalCost.replace('$', '');  // Remove the $ sign
                totalSubContractorsTotalCost = parseFloat(totalSubContractorsTotalCost);  // Convert the string to a floating point number


                var totalSubContractorsTotalCost2 = $('#totalcountequip').text();  // Get the text including $
                totalSubContractorsTotalCost2 = totalSubContractorsTotalCost2.replace('$', '');  // Remove the $ sign
                totalSubContractorsTotalCost2 = parseFloat(totalSubContractorsTotalCost2);

                var totalSubContractorsTotalCost3 = $('#totalcountmaterial').text();  // Get the text including $
                totalSubContractorsTotalCost3 = totalSubContractorsTotalCost3.replace('$', '');  // Remove the $ sign
                totalSubContractorsTotalCost3 = parseFloat(totalSubContractorsTotalCost3);

                var totalSubContractorsTotalCost4 = $('#totalcountother').text();  // Get the text including $
                totalSubContractorsTotalCost4 = totalSubContractorsTotalCost4.replace('$', '');  // Remove the $ sign
                totalSubContractorsTotalCost4 = parseFloat(totalSubContractorsTotalCost4);
                // alert(totalSubContractorsTotalCost3);
                var totalSubContractorsTotalCost5 = $('#totalSubContractorsValu').text();  // Get the text including
                totalSubContractorsTotalCost5 = totalSubContractorsTotalCost5.replace('$', '');  // Remove the $ sign
                totalSubContractorsTotalCost5 = parseFloat(totalSubContractorsTotalCost5);

                var tyt = totalSubContractorsTotalCost + totalSubContractorsTotalCost2 + totalSubContractorsTotalCost3 + totalSubContractorsTotalCost4 + totalSubContractorsTotalCost5;

                $('#totalallcount').text('$' + tyt.toFixed(2));  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    SubContractorsTotalObserver.observe(totalCountSubContractorsTotalElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalCountSubContractorsTotalElement.textContent = "${{$totalCostSumcontractor}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountlaborPrice)
    const totalcountlaborPriceElementCont = document.getElementById('totalSubContractorsValuPrice');

    // MutationObserver ka instance create karna
    const totalcountlaborPriceObserver = new MutationObserver(function (mutationsList, totalcountlaborPriceObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var laborPriceValue = $('#totalcountlaborPrice').text();  // Get the text including $
                laborPriceValue = laborPriceValue.replace('$', '');  // Remove the $ sign
                laborPriceValue = parseFloat(laborPriceValue);  // Convert the string to a floating point number

                var equipPriceValue = $('#totalcountequipPrice').text();  // Get the text including $
                equipPriceValue = equipPriceValue.replace('$', '');  // Remove the $ sign
                equipPriceValue = parseFloat(equipPriceValue);

                var materialPriceValue = $('#totalcountmaterialPrice').text();  // Get the text including $
                materialPriceValue = materialPriceValue.replace('$', '');  // Remove the $ sign
                materialPriceValue = parseFloat(materialPriceValue);

                var otherPriceValue = $('#totalcountotherPrice').text();  // Get the text including $
                otherPriceValue = otherPriceValue.replace('$', '');  // Remove the $ sign
                otherPriceValue = parseFloat(otherPriceValue);

                var subContractorPriceValue = $('#totalSubContractorsValuPrice').text();  // Get the text including $
                subContractorPriceValue = subContractorPriceValue.replace('$', '');  // Remove the $ sign
                subContractorPriceValue = parseFloat(subContractorPriceValue);

                var totalPriceValue = laborPriceValue + equipPriceValue + materialPriceValue + otherPriceValue + subContractorPriceValue;

                $('#totalPriceCount').text('$' + totalPriceValue.toFixed(2));  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    totalcountlaborPriceObserver.observe(totalcountlaborPriceElementCont, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalcountlaborPriceElementCont.textContent = "${{$totalPriceSumcontractor}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    // Target element (totalcountmaterial)
    const totalCountTPriceTotalElement = document.getElementById('totalPriceCount');

    // MutationObserver ka instance create karna
    const TPriceTotalObserver = new MutationObserver(function (mutationsList, TPriceTotalObserver) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var totalTPriceTotalCost = $('#totalPriceCount').text();  // Get the text including $
                totalTPriceTotalCost = totalTPriceTotalCost.replace('$', '');  // Remove the $ sign
                totalTPriceTotalCost = parseFloat(totalTPriceTotalCost);  // Convert the string to a floating point number

                var totalTPriceTotalCost33 = $('#totalallcount').text();  // Get the text including $
                totalTPriceTotalCost33 = totalTPriceTotalCost33.replace('$', '');  // Remove the $ sign
                totalTPriceTotalCost33 = parseFloat(totalTPriceTotalCost33);

                $('#totalSellingPrice').text('$' + totalTPriceTotalCost.toFixed(2));  // Display the total cost
                $('#totalSellingCost').text('$' + totalTPriceTotalCost33.toFixed(2));  // Display the total cost

            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    TPriceTotalObserver.observe(totalCountTPriceTotalElement, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalCountTPriceTotalElement.textContent = "${{$totalPriceSumss}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);


    const totalCountTPriceTotalElementTGM = document.getElementById('totalSellingPrice');

    // MutationObserver ka instance create karna
    const TPriceTotalObserverTGM = new MutationObserver(function (mutationsList, TPriceTotalObserverTGM) {
        mutationsList.forEach(mutation => {
            if (mutation.type === 'childList') {
                // Jab text change ho, to yeh function call hoga
                var totalTPriceTotalCostTGM = $('#totalSellingPrice').text();

                totalTPriceTotalCostTGM = totalTPriceTotalCostTGM.replace('$', '');

                totalTPriceTotalCostTGM = parseFloat(totalTPriceTotalCostTGM);

                var ttlall = $('#totalallcount').text();
                console.info(ttlall);
                ttlall = ttlall.replace('$', '');
                var ttl = parseFloat(ttlall);
                var tgmminus = totalTPriceTotalCostTGM - ttl;
                if (totalTPriceTotalCostTGM == '0') {
                    return;
                }
                var gmttl = (tgmminus / totalTPriceTotalCostTGM) * 100;

                $('#totalaveragegrossmargenadded').text(gmttl.toFixed(2) + '%');  // Display the total cost
            }
        });
    });

    // Observer ko target element per observe karna shuru karna
    TPriceTotalObserverTGM.observe(totalCountTPriceTotalElementTGM, {childList: true});

    // Example: Text ko dynamically change karne ka tareeqa
    setTimeout(() => {
        totalCountTPriceTotalElementTGM.textContent = "${{$totalPriceSumss}}";  // Yeh jab bhi text change hoga, observer usay detect karega
    }, 2000);

</script>--}}

<script>
    $(document).ready(function () {

        $('#category_list').change(function () {
            if ($('#category_list').val() == 'hard_materials') {

                $('#depth_fields').show();
                $('#sqft_fields').show();
            } else {
                $('#depth_fields').hide();
                $('#sqft_fields').hide();
            }
        }).trigger('change'); // Trigger change to set initial state
    });

    function sumEquipmentCount(cls) {
        let total = 0;
        // Sabhi `td` elements jinke paas class `equipmentcount` hai, unhe select karte hain
        const elements = document.querySelectorAll('.' + cls);
        console.info(elements);

        // Har element ka text get karke, use integer men convert karte hain aur sum karte hain
        elements.forEach((element) => {
            let value = parseFloat(element.textContent) || 0; // Text ko integer men convert karte hain
            total += value;
        });

        return total;
    }
    function emptyModelFields() {

        // var item_gross_margin=$('#item_gross_margin').val('');
        var item_unitcost = $('#item_unitcost').val('');
        var item_uom = $('#item_uom').val('');
        var item_quantity = $('#item_quantity').val('');
        var item_name = $('#item_name').val('');
        // var category_list=$('#category_list').val('');
    }
    function showhardItem() {
        $('#hardmaterials').css('display', 'block');
        $('#plantmaterials').css('display', 'none');
        $('.hardbtnactive').addClass('hard_active');
        $('.hardbtnactive').removeClass('remove_active');
        $('.plantbtnactive').removeClass('hard_active');
        $('.plantbtnactive').addClass('remove_active');

    }
    function showplantItem() {
        $('#hardmaterials').css('display', 'none');
        $('#plantmaterials').css('display', 'block');
        $('.hardbtnactive').removeClass('hard_active');
        $('.hardbtnactive').addClass('remove_active');
        $('.plantbtnactive').addClass('hard_active');
        $('.plantbtnactive').removeClass('remove_active');
    }
</script>
<script>
    function changeQuantityVal25(c, type, uom, lb, quantityequ, grossmarginequ, unitcostsequ, totalcostscontr, tpoall, labortype) {
        // alert(quantityequ);
        let calculatedQty = parseFloat($('#calculatedQty-' + c).val()) || 0;
        let manualQty = parseFloat($('#' + quantityequ + '-' + c).val()) || 0;

        var qu = calculatedQty + manualQty; // Now both are numbers, so addition will work

        var gm = $('#' + grossmarginequ + '-' + c).val();
        if (type == "contractors") {
            var uc = $('#' + unitcostsequ + '-' + c).val();
        } else {
            var uc = $('#' + unitcostsequ + '-' + c).text();
        }

        var totalcosts = qu * uc;
        let totalrevenue = 0;
        if(type == 'contractors') {
            totalrevenue = parseFloat(uc) * (1 + (gm / 100));
        }else {
            totalrevenue = parseFloat(uc) / (1 - (gm / 100));
        }

        var up = parseFloat(totalrevenue);
        // alert('qu ' +up);

        $('#upidss-' + c).text(parseFloat(up).toFixed(2));


        // alert(up);
        $('#' + totalcostscontr + '-' + c).empty().text(totalcosts);
        // $('#unitprice-' + c).text(parseFloat(up).toFixed(2));
        var tp = up * qu;

        // alert(tp);

        $('#unitprice-' + c).css('white-space', 'nowrap').empty().text(parseFloat(up).toFixed(2));
        $('#' + tpoall + '-' + c).empty().text(parseFloat(tp).toFixed(2));
        if (type == "equipment") {
            var ttl = sumEquipmentCount('equipmentcount');
            $('#totalcountequip').text('$' + ttl);
            $('#sumofmarginequ-' + c).val(totalrevenue * qu);
            var ttlp = sumEquipmentCount('equipmenttotalprice');
            //   var typ=parseFloat(tp) + parseFloat(ttlp);
            $('#totalcountequipPrice').text('$' + ttlp.toFixed(2));
        } else if (type == "labors") {
            var ttl = sumEquipmentCount('laborcount');
            let sum = 0;
            let sum2 = 0;
            $('#sumofmarginlab-' + c).val(totalrevenue * qu);
            var ttlp = sumEquipmentCount('labortotalprice');
            //   var typ=parseFloat(tp) + parseFloat(ttlp);
            $('#totalcountlaborPrice').text('$' + ttlp.toFixed(2));

            // Get all inputs with the class 'totalhourquantity'
            if (labortype == 'Laborers') {
                $('.totalhourslabors').each(function () {
                    let value = parseFloat($(this).val()) || 0; // Convert value to a number, handle NaN
                    sum += value;
                });
                $('#totalhourslabors').text(sum);
            } else if (labortype == 'Supervision') {
                $('.totalhourssupervisions').each(function () {
                    let value = parseFloat($(this).val()) || 0; // Convert value to a number, handle NaN
                    sum2 += value;
                });
                $('#totalhourssupervision').text(sum2);
            }

            console.info(sum);


            $('#totalcountlabor').text('$' + ttl);

            // var lb=data.labor_burden;
            var totalburden = totalcosts * (lb / 100);
            var ttl2 = sumEquipmentCount('burdencount');
            // alert(totalburden);
            //       var ty2=parseFloat(totalburden) + parseFloat(ttl2);
            var bdn = totalburden.toFixed(2);
            $('#totalcountburden').text('$' + bdn);
        } else if (type == "hard_materials") {
            var ttlp = sumEquipmentCount('materialtotalprice');
            //   var typ=parseFloat(tp) + parseFloat(ttlp);
            $('#totalcountmaterialPrice').text('$' + ttlp.toFixed(2));
            var ttl = sumEquipmentCount('materialcount');
            $('#sumofmarginhar-' + c).val(totalrevenue * qu);

            $('#totalcountmaterial').text('$' + ttl);
        } else if (type == "plant_materials") {
            var ttlp = sumEquipmentCount('materialtotalprice');
            //   var typ=parseFloat(tp) + parseFloat(ttlp);
            $('#totalcountmaterialPrice').text('$' + ttlp.toFixed(2));
            $('#sumofmarginpla-' + c).val(totalrevenue * qu);
            var ttl = sumEquipmentCount('materialcount');
            $('#totalcountmaterial').text('$' + ttl);
        } else if (type == "other_costs") {
            var ttlp = sumEquipmentCount('othertotalprice');
            //   var typ=parseFloat(tp) + parseFloat(ttlp);
            $('#totalcountotherPrice').text('$' + ttlp.toFixed(2));
            $('#sumofmarginoth-' + c).val(totalrevenue * qu);
            // alert(totalrevenue*qu);
            var ttl = sumEquipmentCount('othercostcount');

            $('#totalcountother').text('$' + ttl);
        } else if (type == "contractors") {
            var ttlp = sumEquipmentCount('contractortotalprice');
            // alert(ttlp);
            //   var typ=parseFloat(tp) + parseFloat(ttlp);
            $('#totalSubContractorsValuPrice').text('$' + ttlp.toFixed(2));
            $('#sumofmargincon-' + c).val(totalrevenue * qu);
            var ttl = sumEquipmentCount('subcontcount');

            $('#totalSubContractorsValu').text('$' + ttl);
        }

        addItemToTemp(type, c, qu, uom, totalcosts, gm);
        const totalHourInputsequ = document.querySelectorAll('.sumofmargin');
        let sumequ = 0;

        // Loop through each input and add its value to the sum
        totalHourInputsequ.forEach(input => {
            let value = parseFloat(input.value) || 0; // Convert value to a number, handle NaN
            sumequ += value;
        });
        // var id=$('.getitemsid-'+c).val();
        if (type == 'equipment') {

            var id = $('.getitemsequipid-' + c).val();
        } else if (type == 'labors') {
            var id = $('.getitemsid-' + c).val();
        } else if (type == 'hard_materials') {
            var id = $('.getitemshardid-' + c).val();
        } else if (type == 'plant_materials') {
            var id = $('.getitemsplantid-' + c).val();
        } else if (type == 'other_costs') {
            var id = $('.getitemsotherid-' + c).val();
        } else if (type == 'contractors') {
            var id = $('.getitemscontractorid-' + c).val();
        }
        // alert(c);
        updateEstimateItems(c, qu, gm, uc, totalcosts, up, tp);
        var tgmequ = parseFloat(sumequ);
        $('#totalaveragegrossmargen').text(tgmequ);

    }

    function debounce(func, delay) {
        let debounceTimer;
        return function (...args) {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => func.apply(this, args), delay);
            console.log('debounceTimer',debounceTimer);
        };
    }

    const changeQuantityVal = debounce(changeQuantityVal25, 1000);

    // const changeQuantityVal = debounce(debouncedChangeQuantityVal, 50000);

    function changeDepth(c,uom) {
        let depth = parseFloat($('#depth-' + c).val()) || 0;
        let sqft = parseFloat($('#sqft-' + c).val()) || 0;
        let manualValue = parseFloat($('#manual-value-' + c).val()) || 0;
        console.info('manualValue',manualValue);
        let depthDivided = depth / 12;

        let sqftTimesDepth = sqft * depthDivided;

        let dividedBy27 = sqftTimesDepth / 27;

        let multipliedByUOM = dividedBy27 * (uom === "Bag" ? 9 : 1);
        let roundedValue = Math.round(multipliedByUOM);
        let finalMultipliedValue = roundedValue * (uom === "Bag" ? 1 : 0.25);


        let finalResult = finalMultipliedValue + manualValue;
        console.info('finalResult',finalResult);

        $('#depth-' + c).val(depth);
        $('#sqft-' + c).val(sqft)
        $('#calculatedQty-' + c).val(finalResult);

    }

    function changeQuanitityBtn(c) {
        // alert('quantity changed');
        var qu = $('#quantitybtn-' + c).val();
        var gm = $('#grossmarginbtn-' + c).val();
        var uc = $('#unitcoctsbtn-' + c).val();
        var totalcosts = qu * uc;
        var totalrevenue = uc * (gm / 100);
        var up = parseFloat(totalrevenue) + parseFloat(uc);
        // alert(up);
        $('#totalcostsbtn-' + c).empty().text(totalcosts);
        // $('#unitprice-' + c).text(parseFloat(up).toFixed(2));
        var tp = up * qu;
        $('#unitpricebtn-' + c).css('white-space', 'nowrap').empty().text(parseFloat(up).toFixed(2));
        $('#totalpricebtn-' + c).empty().text(parseFloat(tp).toFixed(2));

    }

    function changeQuantityVal24(
        c, type, uom, lb,
        quantityequ, grossmarginequ, unitcostsequ,
        totalcostscontr, tpoall, labortype
    ) {
        // 1. Calculate Quantity
        let quantity = 0;
        const calculatedQty = parseFloat($(`#calculatedQty-${c}`).val()) || 0;
        const manualQty = parseFloat($(`#${quantityequ}-${c}`).val()) || 0;

        if (type === "hard_materials") {
            quantity = calculatedQty + manualQty;
        } else {
            quantity = manualQty;
        }

        // 2. Get Gross Margin and Unit Cost
        const grossMargin = parseFloat($(`#${grossmarginequ}-${c}`).val()) || 0;
        const unitCost = parseFloat(type === "contractors" ? $(`#${unitcostsequ}-${c}`).val() : $(`#${unitcostsequ}-${c}`).text()) || 0;

        // 3. Calculate Total Costs & Revenue
        const totalCost = quantity * unitCost;
        const unitPrice = type === "contractors" ? unitCost * (1 + (grossMargin / 100)) : unitCost / (1 - (grossMargin / 100));
        const totalPrice = unitPrice * quantity;

        // 4. Update DOM with calculated values
        $(`#upidss-${c}`).text(unitPrice.toFixed(2));
        $(`#${totalcostscontr}-${c}`).text(totalCost);
        $(`#unitprice-${c}`).text(unitPrice.toFixed(2));
        $(`#${tpoall}-${c}`).text(totalPrice.toFixed(2));

        // 5. Type-specific calculations
        const revenue = unitPrice * quantity;

        switch (type) {
            case "equipment":
                $(`#sumofmarginequ-${c}`).val(revenue);
                $('#totalcountequip').text(`$${sumEquipmentCount('equipmentcount')}`);
                $('#totalcountequipPrice').text(`$${sumEquipmentCount('equipmenttotalprice').toFixed(2)}`);
                break;

            case "labors":
                $(`#sumofmarginlab-${c}`).val(revenue);
                $('#totalcountlabor').text(`$${sumEquipmentCount('laborcount')}`);
                $('#totalcountlaborPrice').text(`$${sumEquipmentCount('labortotalprice').toFixed(2)}`);

                let hours = 0;
                const hourSelector = labortype === 'Laborers' ? '.totalhourslabors' : '.totalhourssupervisions';
                $(hourSelector).each(function () {
                    hours += parseFloat($(this).val()) || 0;
                });

                const hourDisplayId = labortype === 'Laborers' ? '#totalhourslabors' : '#totalhourssupervision';
                $(hourDisplayId).text(hours);

                const burden = totalCost * (lb / 100);
                $('#totalcountburden').text(`$${burden.toFixed(2)}`);
                break;

            case "hard_materials":
            case "plant_materials":
                const marginField = type === "hard_materials" ? 'sumofmarginhar' : 'sumofmarginpla';
                $(`#${marginField}-${c}`).val(revenue);
                $('#totalcountmaterial').text(`$${sumEquipmentCount('materialcount')}`);
                $('#totalcountmaterialPrice').text(`$${sumEquipmentCount('materialtotalprice').toFixed(2)}`);
                break;

            case "other_costs":
                $(`#sumofmarginoth-${c}`).val(revenue);
                $('#totalcountother').text(`$${sumEquipmentCount('othercostcount')}`);
                $('#totalcountotherPrice').text(`$${sumEquipmentCount('othertotalprice').toFixed(2)}`);
                break;

            case "contractors":
                $(`#sumofmargincon-${c}`).val(revenue);
                $('#totalSubContractorsValu').text(`$${sumEquipmentCount('subcontcount')}`);
                $('#totalSubContractorsValuPrice').text(`$${sumEquipmentCount('contractortotalprice').toFixed(2)}`);
                break;
        }

        // 6. Add item to temp and update summary
        addItemToTemp(type, c, quantity, uom, totalCost, grossMargin);

        let sumOfMargins = 0;
        document.querySelectorAll('.sumofmargin').forEach(input => {
            sumOfMargins += parseFloat(input.value) || 0;
        });

        $('#totalaveragegrossmargen').text(sumOfMargins);

        // 7. Update Estimate Items
        let itemId = $(`.getitems${type.replace('_', '')}id2-${c}`).val();
        updateEstimateItems2(c, quantity, grossMargin, unitCost, totalCost, unitPrice, totalPrice);
    }


    function debounce(func, delay) {
        let debounceTimer;
        console.info("Debounce reset. Waiting for delay...");
        return function (...args) {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => func.apply(this, args), delay);
            console.log('debounceTimer',debounceTimer);
        };
    }

    const changeQuantityVal2 = debounce(changeQuantityVal24, 1000);
</script>
<script>
    function randUnique2(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    function addNewItem() {
        var classf = document.getElementsByClassName('accordbody');
        var c = randUnique2(1000, 9999);
        var df = `<tr class="border datarowsequ-${c}">
                                                        <td class="drag-handle">
                                                            <svg
                                                                width="17"
                                                                height="22"
                                                                viewBox="0 0  17 22"
                                                                fill="none"
                                                                xmlns="http://www.w3.org/2000/svg"
                                                            >
                                                                <path
                                                                    d="M5.29167 11.9166C5.79793 11.9166 6.20833 11.5062 6.20833 11C6.20833 10.4937 5.79793 10.0833 5.29167 10.0833C4.78541 10.0833 4.375 10.4937 4.375 11C4.375 11.5062 4.78541 11.9166 5.29167 11.9166Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                                <path
                                                                    d="M5.29167 5.49996C5.79793 5.49996 6.20833 5.08955 6.20833 4.58329C6.20833 4.07703 5.79793 3.66663 5.29167 3.66663C4.78541 3.66663 4.375 4.07703 4.375 4.58329C4.375 5.08955 4.78541 5.49996 5.29167 5.49996Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                                <path
                                                                    d="M5.29167 18.3333C5.79793 18.3333 6.20833 17.9229 6.20833 17.4166C6.20833 16.9104 5.79793 16.5 5.29167 16.5C4.78541 16.5 4.375 16.9104 4.375 17.4166C4.375 17.9229 4.78541 18.3333 5.29167 18.3333Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                                <path
                                                                    d="M11.7096 11.9166C12.2159 11.9166 12.6263 11.5062 12.6263 11C12.6263 10.4937 12.2159 10.0833 11.7096 10.0833C11.2034 10.0833 10.793 10.4937 10.793 11C10.793 11.5062 11.2034 11.9166 11.7096 11.9166Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                                <path
                                                                    d="M11.7096 5.49996C12.2159 5.49996 12.6263 5.08955 12.6263 4.58329C12.6263 4.07703 12.2159 3.66663 11.7096 3.66663C11.2034 3.66663 10.793 4.07703 10.793 4.58329C10.793 5.08955 11.2034 5.49996 11.7096 5.49996Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                                <path
                                                                    d="M11.7096 18.3333C12.2159 18.3333 12.6263 17.9229 12.6263 17.4166C12.6263 16.9104 12.2159 16.5 11.7096 16.5C11.2034 16.5 10.793 16.9104 10.793 17.4166C10.793 17.9229 11.2034 18.3333 11.7096 18.3333Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                            </svg>
                                                        </td>
                                                        <td class="border">
                                                            <label for=""
                                                                ><input style="width: 100%;" type="text" oninput="changeQuanitityBtn(${c})" placeholder="Item name" /></label
                                                            >
                                                        </td>

                                                        <td class="border" colspan="1"><input value="1" id="quantitybtn-${c}" oninput="changeQuanitityBtn(${c})" type="number" style="width: 100%;" placeholder="01" /></td>
                                                         <td class="border" colspan="1"><input type="text" oninput="changeQuanitityBtn(${c})" style="width: 100%;" placeholder="Daily/weekly" /></td>
                                                        <td class="border" colspan="1"><input type="number" oninput="changeQuanitityBtn(${c})" id="unitcoctsbtn-${c}" style="width: 100%;" placeholder="Unit cost" value="1" /></td>
                                                        <td class="border" id="totalcostsbtn-${c}" colspan="1"></td>
                                                        <td class="border" colspan="1"><input type="text" oninput="changeQuanitityBtn(${c})" value="1" id="grossmarginbtn-${c}" style="width: 100%;" placeholder="01" />
                                                            </td>
                                                        <td class="border" id="totalpricebtn-${c}" colspan="1"></td>
                                                        <td class="border" id="unitpricebtn-${c}"></td>

                                                        <td class="border">
                                                            <a href="javascript:void(0)" onclick="deletedatarowsequ(${c})">
                                                                <svg
                                                                    width="16"
                                                                    height="16"
                                                                    viewBox="0 0 16 16"
                                                                    fill="none"
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                >
                                                                    <path
                                                                        d="M5.5 0.5H10.5M0.5 3H15.5M13.8333 3L13.2489 11.7661C13.1612 13.0813 13.1174 13.7389 12.8333 14.2375C12.5833 14.6765 12.206 15.0294 11.7514 15.2497C11.235 15.5 10.5759 15.5 9.25779 15.5H6.74221C5.42409 15.5 4.76503 15.5 4.24861 15.2497C3.79396 15.0294 3.41674 14.6765 3.16665 14.2375C2.88259 13.7389 2.83875 13.0813 2.75107 11.7661L2.16667 3"
                                                                        stroke="red"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"
                                                                    />
                                                                </svg>
                                                            </a>
                                                        </td>
                                                    </tr>`;
        // if (classf.length > 0) {

        $('#sortable-table-body').append(df);
        // }else{
        //     alert('First open any section');
        // }

    }

    function addNewItemModel() {
        var classf = document.getElementsByClassName('accordbody');
        // var c = randUnique2(1000, 9999);
        var getdivisions = '0';
        var item_uom = $('#item_uom').val();

        var category_list = $('#category_list').val();
        if(category_list == 'hard_materials')
        {
            let sqft = $('#sqft').val();
            console.info('sqft',sqft);
            let depth = $("#depth").val();
            let depthDivided = depth / 12;

            let sqftTimesDepth = sqft * depthDivided;

            let dividedBy27 = sqftTimesDepth / 27;



            let multipliedByUOM = dividedBy27 * (item_uom === "Bag" ? 9 : 1);
            let roundedValue = Math.round(multipliedByUOM);
            let finalMultipliedValue = roundedValue * (item_uom === "Bag" ? 1 : 0.25);
            //var item_quantity = parseFloat(finalMultipliedValue) + parseFloat($('#item_quantity').val()) || 0;
            var depthSqftValue = parseFloat(finalMultipliedValue) + parseFloat($('#item_quantity').val()) || 0;
            var item_quantity = parseFloat($('#item_quantity').val()) || 0;
        } else {
            var item_quantity = $('#item_quantity').val();
        }
        // alert('yes clicked');
        var item_gross_margin = $('#item_gross_margin').val();
        var item_unitcost = $('#item_unitcost').val();

        var item_name = $('#item_name').val();

        var getdivision = $('#getdivision').val();
        let depth = $('#depth').val();
        let sqft = $('#sqft').val();
        var data = {
            'item_gross_margin': item_gross_margin,
            'item_unitcost': item_unitcost,
            'item_uom': item_uom,
            'item_quantity': item_quantity,
            'item_name': item_name,
            'category_list': category_list,
            'division_id': getdivisions,
            'depth': depth,
            'sqft': sqft,
        }
        // alert(category_list);
        var url = "{{ URL::route(getRouteAlias() . '.division.addNewItem') }}";
        if (category_list == '' || item_name == '' || item_uom == '' || item_unitcost == '' || item_gross_margin == '') {
            alert('Category, Item name, item uom, item unit cost and gross margin are required fields');
            return;
        }
        $.ajax({
            method: "get",
            url: url,
            data: data,
            success: function (response) {
                console.log(response);

                // var getdivision=$('#getdivision').val('');

                // // $('#accordion-container').html(response.data)
                if (category_list == 'equipment') {
                    var mclass = 'datarowsequ';
                    var tclass = "totalcostsequip";
                    var qtyclass = "quantityequ";
                    var ucclass = "unitcostsequ";
                    var gmclass = "grossmarginequ";


                } else if (category_list == 'labors') {
                    var mclass = 'datarowsLabor';
                    var tclass = "totalcostslabo";
                    var qtyclass = "quantitylabo";
                    var ucclass = "unitcostslabo";
                    var gmclass = "grossmarginlabo";
                } else if (category_list == 'hard_materials') {
                    var mclass = 'datarowshard';
                    var tclass = "totalcostshard";
                    var qtyclass = "quantityhar";
                    var ucclass = "unitcostshar";
                    var gmclass = "grossmarginhar";
                } else if (category_list == 'plant_materials') {
                    var mclass = 'datarowsplant';
                    var tclass = "totalcostsplant";
                    var qtyclass = "quantitypla";
                    var ucclass = "unitcostspla";
                    var gmclass = "grossmarginpla";
                } else if (category_list == 'other_costs') {
                    var mclass = 'datarowsother';
                    var tclass = "totalcostscosts";
                    var qtyclass = "quantitycos";
                    var ucclass = "unitcostscos";
                    var gmclass = "grossmargincos";
                } else if (category_list == 'contractors') {
                    var mclass = 'datarowsContractor';
                    var tclass = "totalcostscontr";
                    var qtyclass = "quantitycontss";
                    var ucclass = "unitcostscont";
                    var gmclass = "grossmargincont";
                }
                var data = response.data;
                var margin = response.margin;
                var qu = item_quantity;
                var gm = margin.default;
                var uc = data.cost;
                var totalcosts = qu * uc;
                var totalrevenue = uc * (gm / 100);
                var up = parseFloat(totalrevenue) + parseFloat(uc);
                // alert(up);
                // $('#totalcosts-'+c).empty().text(totalcosts);
                // $('#unitprice-' + c).text(parseFloat(up).toFixed(2));
                var tp = up * qu;
                if (category_list == "equipment") {
                    var totalcostsequip = "totalcostsequip";
                    var unitcostsequ = "unitcostsequ";
                    var grossmarginequ = "grossmarginequ";
                    var quantityequ = "quantityequ";
                    var totalpriceequipmn = "totalpriceequi";
                    var ttl = sumEquipmentCount('equipmentcount');
                    $('#sumofmarginequ-' + c).val(totalrevenue * qu);
                    var qcl = "equipmentcount";
                    var uomc = data.uom;
                    var ty = parseFloat(totalcosts) + parseFloat(ttl);
                    $('#totalcountequip').text('$' + ty);

                    var ttlp = sumEquipmentCount('equipmenttotalprice');
                    var tpclass = "equipmenttotalprice";
                    var typ = parseFloat(tp) + parseFloat(ttlp);
                    $('#totalcountequipPrice').text('$' + typ.toFixed(2));

                } else if (category_list == "labors") {
                    $('#sumofmarginlab-' + c).val(totalrevenue * qu);
                    var ttl = sumEquipmentCount('laborcount');
                    var qcl = "laborcount";
                    var uomc = data.uom;
                    var ty = parseFloat(totalcosts) + parseFloat(ttl);
                    $('#totalcountlabor').text('$' + ty);
                    let sum = 0;

// Get all inputs with the class 'totalhourquantity'
                    $('.totalhourquantity').each(function () {
                        let value = parseFloat($(this).val()) || 0; // Convert value to a number, handle NaN
                        sum += value;
                    });
                    var thuq = parseFloat(sum) + parseFloat(qu);
                    $('#totalhourslabors').text(thuq);
                } else if (category_list == "hard_materials") {

                    var totalcostsequip = "totalcostshard";
                    var unitcostsequ = "unitcostshar";
                    var grossmarginequ = "grossmarginhar";
                    var quantityequ = "quantityhar";
                    var totalpriceequipmn = "totalpricehardm";


                    $('#sumofmarginhar-' + c).val(totalrevenue * qu);
                    var ttl = sumEquipmentCount('materialcount');
                    var qcl = "materialcount";
                    var uomc = data.uom;
                    var ty = parseFloat(totalcosts) + parseFloat(ttl);
                    var ttlp = sumEquipmentCount('materialtotalprice');
                    var tpclass = "materialtotalprice";
                    var typ = parseFloat(tp) + parseFloat(ttlp);
                    $('#totalcountmaterialPrice').text('$' + typ.toFixed(2));
                    $('#totalcountmaterial').text('$' + ty);
                } else if (category_list == "plant_materials") {

                    var totalcostsequip = "totalcostsplant";
                    var unitcostsequ = "unitcostspla";
                    var grossmarginequ = "grossmarginpla";
                    var quantityequ = "quantitypla";
                    var totalpriceequipmn = "totalpriceplantm";


                    $('#sumofmarginpla-' + c).val(totalrevenue * qu);
                    var ttl = sumEquipmentCount('materialcount');
                    var ty = parseFloat(totalcosts) + parseFloat(ttl);
                    var qcl = "materialcount";
                    var uomc = data.size;
                    $('#totalcountmaterial').text('$' + ty);
                    var ttlp = sumEquipmentCount('materialtotalprice');
                    var tpclass = "materialtotalprice";
                    var typ = parseFloat(tp) + parseFloat(ttlp);
                    $('#totalcountmaterialPrice').text('$' + typ.toFixed(2));
                } else if (category_list == "other_costs") {

                    var totalcostsequip = "totalcostscosts";
                    var unitcostsequ = "unitcostscos";
                    var grossmarginequ = "grossmargincos";
                    var quantityequ = "quantitycos";
                    var totalpriceequipmn = "totalpriceothercost";


                    var ttlp = sumEquipmentCount('othertotalprice');
                    var tpclass = "othertotalprice";
                    var typ = parseFloat(tp) + parseFloat(ttlp);
                    $('#totalcountotherPrice').text('$' + typ.toFixed(2));


                    $('#sumofmarginoth-' + c).val(totalrevenue * qu);
                    var ttl = sumEquipmentCount('othercostcount');
                    var qcl = "othercostcount";
                    var uomc = data.uom;
                    var ty = parseFloat(totalcosts) + parseFloat(ttl);
                    $('#totalcountother').text('$' + ty);
                } else if (category_list == "contractors") {

                    var totalcostsequip = "totalcostscontr";
                    var unitcostsequ = "unitcostscont";
                    var grossmarginequ = "grossmargincont";
                    var quantityequ = "quantitycontss";
                    var totalpriceequipmn = "totalpricecontractorm";


                    var ttlp = sumEquipmentCount('contractortotalprice');
                    var tpclass = "contractortotalprice";
                    var typ = parseFloat(tp) + parseFloat(ttlp);
                    $('#totalSubContractorsValuPrice').text('$' + typ.toFixed(2));


                    $('#sumofmargincon-' + c).val(totalrevenue * qu);
                    var ttl = sumEquipmentCount('subcontcount');
                    var qcl = "subcontcount";
                    var uomc = data.uom;
                    var ty = parseFloat(totalcosts) + parseFloat(ttl);
                    $('#totalSubContractorsValu').text('$' + ty);
                }
                const totalHourInputsequ2 = document.querySelectorAll('.sumofmargin');
                let sumequ = 0;

// Loop through each input and add its value to the sum
                totalHourInputsequ2.forEach(input => {
                    let value = parseFloat(input.value) || 0; // Convert value to a number, handle NaN
                    sumequ += value;
                });

                var tgmequ = parseFloat(sumequ);
                $('#totalaveragegrossmargen').text(tgmequ);
                var c = data.id;

                var section_name = '';
                if (classf.length > 0) {
                    section_name = $('.editsectionid').val();
                }
                saveBatchItems(category_list, c, item_quantity, data.uom, totalcosts, margin.default, data.cost, up, tp, section_name,depth,sqft, function (id) {
                    console.info('Returned ID:', id);
                    var c2 = id; // Now `c2` will have the `id` value

                    const extraTds = (category_list === 'hard_materials') ? `<td class="border" style="width:9%;">
                                                        <div class="d-flex">
                                                        <span class="getDetailsOfItem" data-id="${id?.id}" style="cursor:pointer;"> <img style="width: 24px; height: 24px; margin-right: 4px;" src="/asset/assets/images/icons8-cube-64.png" alt=""> </span>
                                                        <!-- <span style="margin-right: 8px; font-size:14px; font-weight:500;">${depthSqftValue}</span>
                                                         <span class="getDetailsOfItem" data-id="${id?.id}" style="cursor:pointer;"><svg class="" width="15" height="15" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M1.87604 17.1156C1.92198 16.7021 1.94496 16.4954 2.00751 16.3022C2.06301 16.1307 2.14143 15.9676 2.24064 15.8171C2.35246 15.6475 2.49955 15.5005 2.79373 15.2063L16 2C17.1046 0.895427 18.8955 0.895428 20 2C21.1046 3.10457 21.1046 4.89543 20 6L6.79373 19.2063C6.49955 19.5005 6.35245 19.6475 6.18289 19.7594C6.03245 19.8586 5.86929 19.937 5.69785 19.9925C5.5046 20.055 5.29786 20.078 4.88437 20.124L1.5 20.5L1.87604 17.1156Z" stroke="#7C8091" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                                        </svg></span> -->
                                                    </div>

                                                        </td>` : `<td></td>`;

                    var df = `<tr class="border ${mclass}-${c2}">
                                                        <td class="drag-handle" style="width: 4%">
                                                            <svg
                                                                width="17"
                                                                height="22"
                                                                viewBox="0 0  17 22"
                                                                fill="none"
                                                                xmlns="http://www.w3.org/2000/svg"
                                                            >
                                                                <path
                                                                    d="M5.29167 11.9166C5.79793 11.9166 6.20833 11.5062 6.20833 11C6.20833 10.4937 5.79793 10.0833 5.29167 10.0833C4.78541 10.0833 4.375 10.4937 4.375 11C4.375 11.5062 4.78541 11.9166 5.29167 11.9166Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                                <path
                                                                    d="M5.29167 5.49996C5.79793 5.49996 6.20833 5.08955 6.20833 4.58329C6.20833 4.07703 5.79793 3.66663 5.29167 3.66663C4.78541 3.66663 4.375 4.07703 4.375 4.58329C4.375 5.08955 4.78541 5.49996 5.29167 5.49996Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                                <path
                                                                    d="M5.29167 18.3333C5.79793 18.3333 6.20833 17.9229 6.20833 17.4166C6.20833 16.9104 5.79793 16.5 5.29167 16.5C4.78541 16.5 4.375 16.9104 4.375 17.4166C4.375 17.9229 4.78541 18.3333 5.29167 18.3333Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                                <path
                                                                    d="M11.7096 11.9166C12.2159 11.9166 12.6263 11.5062 12.6263 11C12.6263 10.4937 12.2159 10.0833 11.7096 10.0833C11.2034 10.0833 10.793 10.4937 10.793 11C10.793 11.5062 11.2034 11.9166 11.7096 11.9166Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                                <path
                                                                    d="M11.7096 5.49996C12.2159 5.49996 12.6263 5.08955 12.6263 4.58329C12.6263 4.07703 12.2159 3.66663 11.7096 3.66663C11.2034 3.66663 10.793 4.07703 10.793 4.58329C10.793 5.08955 11.2034 5.49996 11.7096 5.49996Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                                <path
                                                                    d="M11.7096 18.3333C12.2159 18.3333 12.6263 17.9229 12.6263 17.4166C12.6263 16.9104 12.2159 16.5 11.7096 16.5C11.2034 16.5 10.793 16.9104 10.793 17.4166C10.793 17.9229 11.2034 18.3333 11.7096 18.3333Z"
                                                                    stroke="#D3D6E0"
                                                                    stroke-width="1.83333"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                />
                                                            </svg>
                                                        </td>
                                                        <td class="border" style="width: 20% !important; border-right: 0 !important;">
                                                         <label id="dynamicLabel">${data.name}</label>
                                                        </td>
                                                        ${extraTds}

                                                        <td class="border" colspan="1" style="width: 12%;">
                                                        <input class="totalhourquantity" type="number" oninput="changeQuantityVal(${c2}, '${category_list}', '${uomc}', '', '${quantityequ}', '${grossmarginequ}', '${unitcostsequ}', '${tclass}', '${totalpriceequipmn}'); if(this.value < 1) this.value = 1;" min="1" value="${item_quantity}" style="width: 100%;" id="${qtyclass}-${c2}" placeholder="01" />
                                                        </td>
                                                        // oninput="changeQuantityVal(${c2}, 'equipment', '${data.uom}', '', 'quantityequ', 'grossmarginequ', 'unitcostsequ', 'totalcostsequip', 'totalpriceequi'); if(this.value < 1) this.value = 1;" value="1" min="1"

                                                        <td class="border" colspan="1" style="width: 9% !important">${uomc}</td>
                                                        <td class="border" style="width: 9% !important" id="${ucclass}-${c2}" colspan="1">${data.cost}</td>
                                                        <td class="border ${qcl}" style="width: 9% !important" id="${tclass}-${c2}" colspan="1">${totalcosts}</td>
                                                        <td class="border" style="width: 12% !important" colspan="1"><input type="number" oninput="changeQuantityVal(${c2}, '${category_list}', '${uomc}', '', '${quantityequ}', '${grossmarginequ}', '${unitcostsequ}', '${totalcostsequip}', '${totalpriceequipmn}'); if(this.value < ${margin.minimum}) this.value = ${margin.minimum};" id="${grossmarginequ}-${c2}" style="width: 100% !important" value="${margin.default}" placeholder="01" /></td>
                                                        <td class="border ${tpclass}" style="width: 9% !important" id="${totalpriceequipmn}-${c2}" colspan="1">${parseFloat(tp).toFixed(2)}</td>
                                                        <td class="border" style="width: 9% !important" id="unitprice-${c2}">${parseFloat(up).toFixed(2)}</td>

                                                        <td class="border" style="text-align: center;">
                                                            <a href="javascript:void(0)" onclick="deletedatarowsequ2(${c2}, '${mclass}', '${category_list}', '')">
                                                                <img src="/asset/assets/images/trash-icon.svg" alt="trash icon">
                                                            </a>
                                                        </td>
                                                    </tr>`;
                    $('#sortable-table-body').append(df);
                });
                addItemToTemp(category_list, data.name, item_quantity, uomc, totalcosts, uc)


                var myModalEl = document.getElementById('addItemModal');
                var modalInstance = bootstrap.Modal.getInstance(myModalEl); // Get the Bootstrap modal instance

                if (modalInstance) {
                    modalInstance.hide(); // Close the modal
                }


            },
            error: function (response) {

            }
        });


    }
</script>
<script>
    function toggleAccordion(event) {
        const button = document.getElementById(event);
        const isOpen = $(button).hasClass('collapsed');
        // alert(isOpen);

        $('.collapse').not(button).each(function () {
            $('.tavy').collapse('hide'); // Hide all accordions except the one clicked
            $('.accord-bodys').removeClass('accordbody');
            var closeIcon = `<svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="12"
                                height="12"
                                fill="currentColor"
                                class="bi bi-chevron-down arrow-icon "
                                viewBox="0 0 16 16"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"
                                />
                            </svg>`;
            $(this).closest('tr').find('.changeicon').empty().append(closeIcon); // Set close icon for all other accordions

            var ictt = `<svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="12"
                                            height="12"
                                            fill="currentColor"
                                            class="bi bi-chevron-down arrow-icon "
                                            viewBox="0 0 16 16"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"
                                            />
                                        </svg>`;
            $('.changeiconall').empty().append(ictt);
        });


        if (isOpen == false) {
            $('#section-initial').collapse('show');
            $('.accord-bodys').addClass('accordbody');
            var ic = `<svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1 5L5 1L9 5" stroke="#667085" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;
            $('.changeicon').empty().append(ic);
        } else {
            var ic = `<svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="12"
                                                    height="12"
                                                    fill="currentColor"
                                                    class="bi bi-chevron-down arrow-icon "
                                                    viewBox="0 0 16 16"
                                                >
                                                    <path
                                                        fill-rule="evenodd"
                                                        d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"
                                                    />
                                                </svg>`;
            $('.changeicon').empty().append(ic);
        }
        // alert(ic);

    }

</script>

<script>
    function toggleAccordion2(event) {
        const buttonId = 'mainbtnnow-' + event;
        const sectionId = 'section-' + event;
        const isOpen = !$('#' + buttonId).hasClass('collapsed');

        // Close all accordions and reset their states
        $('.collapse').not('#' + sectionId).each(function () {
            $('.accord-bodys').removeClass('accordbody');
            $('.editinput').removeClass('editsection');
            $('.editinputid').removeClass('editsectionid');

            // Set collapsed icon
            const collapsedIcon = `
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                 class="bi bi-chevron-down arrow-icon" viewBox="0 0 16 16">
                <path fill-rule="evenodd"
                      d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>
            </svg>`;
            $(this).closest('tr').find('.changeicon').empty().append(collapsedIcon);
        });

        // Toggle logic
        if (!isOpen) {
            // Open the accordion
            $('#' + sectionId).collapse('show');
            $('.accord-bodys-' + event).addClass('accordbody');
            $('#editinput-' + event).addClass('editsection');
            $('#editinputid-' + event).addClass('editsectionid');

            // Up arrow icon
            const expandedIcon = `
            <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 5L5 1L9 5" stroke="#667085" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>`;
            $('.changeicon-' + event).empty().append(expandedIcon);

            $(`#sectionParent-${event}`).removeClass('section-parent');
            $(`#section-${event} td div`).removeClass('section-child');
        } else {
            // Close icon (default down arrow)
            const collapsedIcon = `
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                 class="bi bi-chevron-down arrow-icon" viewBox="0 0 16 16">
                <path fill-rule="evenodd"
                      d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>
            </svg>`;
            $('.changeicon-' + event).empty().append(collapsedIcon);

            $(`#sectionParent-${event}`).addClass('section-parent');
            $(`#section-${event} td div`).addClass('section-child');
        }
    }
</script>
<script>
    function addItemToFavourite(id, type) {
        var data = {
            'id': id,
            'type': type
        }
        var url = "{{ URL::route(getRouteAlias() . '.division.addFavItem') }}";
        $.ajax({
            method: "get",
            url: url,
            data: data,
            success: function (response) {
                console.log(response);
                if (response.success == true) {
                    if (type == "equipment") {
                        $(`#nofav-${id}`).replaceWith(`
        <svg width="16" id="yesfav-${id}" onclick="deleteItemToFavourite(${id}, 'equipment')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path stroke="red"  d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    } else if (type == "hard_materials") {
                        $(`#nofavhard-${id}`).replaceWith(`
        <svg width="16" id="yesfavhard-${id}" onclick="deleteItemToFavourite(${id}, 'hard_materials')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path stroke="red"  d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    } else if (type == "plant_materials") {
                        $(`#nofavplant-${id}`).replaceWith(`
        <svg width="16" id="yesfavplant-${id}" height="14" onclick="deleteItemToFavourite(${id}, 'plant_materials')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path stroke="red"  d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    } else if (type == "other_costs") {
                        $(`#nofavother-${id}`).replaceWith(`
        <svg width="16" id="yesfavother-${id}" height="14" onclick="deleteItemToFavourite(${id}, 'other_costs')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path stroke="red"  d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    } else if (type == "labors") {
                        $(`#nofavlabor-${id}`).replaceWith(`
        <svg width="16" id="yesfavlabor-${id}" onclick="deleteItemToFavourite(${id}, 'labors')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path stroke="red"  d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    } else if (type == "contractors") {
                        $(`#nofavcontractor-${id}`).replaceWith(`
        <svg width="16" id="yesfavcontractor-${id}" onclick="deleteItemToFavourite(${id}, 'contractors')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path stroke="red"  d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    }

                }


            },
            error: function (response) {

            }
        });
    }

    function deleteItemToFavourite(id, type) {
        var data = {
            'id': id,
            'type': type
        }
        var url = "{{ URL::route(getRouteAlias() . '.division.deleteFavItem') }}";
        $.ajax({
            method: "get",
            url: url,
            data: data,
            success: function (response) {
                console.log(response);
                if (response.success == true) {
                    if (type == "equipment") {
                        $(`#yesfav-${id}`).replaceWith(`
        <svg width="16" id="nofav-${id}" onclick="addItemToFavourite(${id}, 'equipment')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    } else if (type == "hard_materials") {
                        $(`#yesfavhard-${id}`).replaceWith(`
        <svg width="16" id="nofavhard-${id}" onclick="addItemToFavourite(${id}, 'hard_materials')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    } else if (type == "plant_materials") {
                        $(`#yesfavplant-${id}`).replaceWith(`
        <svg width="16" id="nofavplant-${id}" onclick="addItemToFavourite(${id}, 'plant_materials')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    } else if (type == "other_costs") {
                        $(`#yesfavother-${id}`).replaceWith(`
        <svg width="16" id="nofavother-${id}" onclick="addItemToFavourite(${id}, 'other_costs')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    } else if (type == "labors") {

                        $(`#yesfavlabor-${id}`).replaceWith(`
        <svg width="16" id="nofavlabor-${id}" onclick="addItemToFavourite(${id}, 'labors')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                    } else if (type == "contractors") {

                        $(`#yesfavcontractor-${id}`).replaceWith(`
<svg width="16" id="nofavcontractor-${id}" onclick="addItemToFavourite(${id}, 'contractors')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`);
                    }

                }


            },
            error: function (response) {

            }
        });
    }

    function deleteItemToFavourite22(id, type) {
        var data = {
            'id': id,
            'type': type
        }
        var url = "{{ URL::route(getRouteAlias() . '.division.deleteFavItem') }}";
        $.ajax({
            method: "get",
            url: url,
            data: data,
            success: function (response) {
                console.log(response);
                if (response.success == true) {
                    if (type == "equipment") {
                        $(`#yesfav-${id}`).replaceWith(`
        <svg width="16" id="nofav-${id}" onclick="addItemToFavourite(${id}, 'equipment')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                        $('#removeequipli-' + id).remove();
                    } else if (type == "hard_materials") {
                        $(`#yesfavhard-${id}`).replaceWith(`
        <svg width="16" id="nofavhard-${id}" onclick="addItemToFavourite(${id}, 'hard_materials')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                        $('#removehardli-' + id).remove();
                    } else if (type == "plant_materials") {
                        $(`#yesfavplant-${id}`).replaceWith(`
        <svg width="16" id="nofavplant-${id}" onclick="addItemToFavourite(${id}, 'plant_materials')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                        $('#removeplantli-' + id).remove();
                    } else if (type == "other_costs") {
                        $(`#yesfavother-${id}`).replaceWith(`
        <svg width="16" id="nofavother-${id}" onclick="addItemToFavourite(${id}, 'other_costs')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                        $('#removeotherli-' + id).remove();
                    } else if (type == "labors") {

                        $(`#yesfavlabor-${id}`).replaceWith(`
        <svg width="16" id="nofavlabor-${id}" onclick="addItemToFavourite(${id}, 'labors')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `);
                        $('#removelaborsli-' + id).remove();
                    } else if (type == "contractors") {

                        $(`#yesfavcontractor-${id}`).replaceWith(`
<svg width="16" id="nofavcontractor-${id}" onclick="addItemToFavourite(${id}, 'contractors')" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`);
                        $('#removecontractorli-' + id).remove();
                    }

                }


            },
            error: function (response) {

            }
        });
    }
</script>

<script>
    function changematerials() {
        var material = document.getElementById('getdivision').value;
        // alert(material);
        location.reload();
        var data = {
            'material': material
        }
        var url = "{{ URL::route(getRouteAlias() . '.division.getdata') }}"
        $.ajax({
            method: "get",
            url: url,
            data: data,
            success: function (response) {
                console.log(response.data);
                $('.allbtn').addClass('samebtn');
                $('.allbtn').removeClass('favtbtn');
                $('.difbtn').removeClass('samebtn');
                $('.difbtn').addClass('favtbtn');
                // $('#accordion-container').html(response.data)
                var eqo = '';
                $.each(response.equipment, function (index, item) {
                    // alert(item.is_favorite);
                    if (item.is_favorite == 0) {
                        var sfg = `<svg width="16" id="nofav-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'equipment')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg = `<svg width="16" onclick="deleteItemToFavourite(${item.id}, 'equipment')" id="yesfav-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
                    eqo += `
                <li class="menu-accordian mt-4">
                      <div class="row">
                          <div class="col-1">
${sfg}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3">
                              <button id="equipmentplusbtn-${item.id}" onclick="addItemTo(${item.id})" style="width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      text-align: center;
                      margin-left: auto;
                      color: white;
                      border: none;
                      font-size: 18px;
                      background-color: #2FCC40;
                      "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>
                          </div>
                      </div>
                  </li>

               `;

                });
                $('#equipmentdata').empty().append(eqo);

                var hardmaterials = '';
                $.each(response.hard_materials, function (index, item) {
                    if (item.is_favorite == 0) {
                        var sfg2 = `<svg width="16" id="nofavhard-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'hard_materials')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg2 = `<svg width="16" onclick="deleteItemToFavourite(${item.id}, 'hard_materials')" id="yesfavhard-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
                    hardmaterials += `
                <li class="menu-accordian mt-4">
                      <div class="row">
                          <div class="col-1">
                             ${sfg2}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3">
                              <button id="hardmplusbtn-${item.id}" onclick="addItemhardM(${item.id})" style="width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      text-align: center;
                      margin-left: auto;
                      color: white;
                      border: none;
                      font-size: 18px;
                      background-color: #2FCC40;
                      "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>
                          </div>
                      </div>
                  </li>

               `;

                });
                $('#hardmaterials').empty().append(hardmaterials);

                var plantmaterials = '';
                $.each(response.plant_materials, function (index, item) {
                    if (item.is_favorite == 0) {
                        var sfg3 = `<svg width="16" id="nofavplant-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'plant_materials')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg3 = `<svg width="16" onclick="deleteItemToFavourite(${item.id}, 'plant_materials')" id="yesfavplant-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
                    plantmaterials += `
                <li class="menu-accordian mt-4">
                      <div class="row">
                          <div class="col-1">
                              ${sfg3}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3">
                              <button id="plantmplusbtn-${item.id}" onclick="addItemhardP(${item.id})" style="width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      text-align: center;
                      margin-left: auto;
                      color: white;
                      border: none;
                      font-size: 18px;
                      background-color: #2FCC40;
                      "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>
                          </div>
                      </div>
                  </li>

               `;

                });
                $('#plantmaterials').empty().append(plantmaterials);
                var othercost = '';
                $.each(response.other_job_costs, function (index, item) {
                    if (item.is_favorite == 0) {
                        var sfg4 = `<svg width="16" id="nofavother-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'other_costs')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg4 = `<svg width="16" onclick="deleteItemToFavourite(${item.id}, 'other_costs')" id="yesfavother-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
                    othercost += `<li class="menu-accordian mt-4">
                      <div class="row">
                          <div class="col-1">
                              ${sfg4}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3">
                              <button id="otherplusbtn-${item.id}" onclick="addItemCost(${item.id})" style="width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      text-align: center;
                      margin-left: auto;
                      color: white;
                      border: none;
                      font-size: 18px;
                      background-color: #2FCC40;
                      "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>
                          </div>
                      </div>
                  </li>

               `;

                });
                $('#othercost').empty().append(othercost);

                var labor = '';
                $.each(response.labors, function (index, item) {
                    if (item.is_favorite == 0) {
                        var sfg5 = `<svg width="16" id="nofavlabor-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'labors')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg5 = `<svg width="16" onclick="deleteItemToFavourite(${item.id}, 'labors')" id="yesfavlabor-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
                    labor += `<li class="menu-accordian mt-4">
                      <div class="row">
                          <div class="col-1">
                              ${sfg5}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3">
                              <button id="laborplusbtn-${item.id}" onclick="addItemLabor(${item.id})" style="width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      text-align: center;
                      margin-left: auto;
                      color: white;
                      border: none;
                      font-size: 18px;
                      background-color: #2FCC40;
                      "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>
                          </div>
                      </div>
                  </li>`;

                });
                $('#laborMenu').empty().append(labor);


                var contractor = '';
                $.each(response.sub_contractor, function (index, item) {
                    console.log(item.id);
                    if (item.is_favorite == 0) {
                        var sfg6 = `<svg width="16" id="nofavcontractor-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'contractors')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg6 = `<svg width="16" onclick="deleteItemToFavourite(${item.id}, 'contractors')" id="yesfavcontractor-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
                    contractor += `<li class="menu-accordian mt-4">
                      <div class="row">
                          <div class="col-1">
                              ${sfg6}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3">
                              <button  id="contractorplusbtn-${item.id}" onclick="addItemContractor(${item.id})" style="width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      text-align: center;
                      margin-left: auto;
                      color: white;
                      border: none;
                      font-size: 18px;
                      background-color: #2FCC40;
                      "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>
                          </div>
                      </div>
                  </li>`;

                });
                $('#sub_contractor_list').empty().append(contractor);

            },
            error: function (response) {

            }
        });
    }

    function getFavItem() {
        var material = document.getElementById('getdivision').value;
        // alert(material);
        var data = {
            'material': material
        }
        var url = "{{ URL::route(getRouteAlias() . '.division.getdataFavItem') }}"
        $.ajax({
            method: "get",
            url: url,
            data: data,
            success: function (response) {
                console.info(response);
                $('.allbtn').removeClass('samebtn');
                $('.allbtn').addClass('favtbtn');
                $('.difbtn').addClass('samebtn');
                $('.difbtn').removeClass('favtbtn');
                // $('#accordion-container').html(response.data)
                var eqo = '';
                $.each(response.equipment, function (index, item) {
                    // alert(item.is_exist);
                    if (item.is_favorite == 0) {
                        var sfg = `<svg width="16" id="nofav-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'equipment')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg = `<svg width="16" onclick="deleteItemToFavourite22(${item.id}, 'equipment')" id="yesfav-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
                    //     if(item.is_exist == 1) {

                    //         var mnbtn=`<button disabled id="equipmentplusbtn-${item.id}" onclick="addItemTo(${item.id})" style="width: 24px;
                    //   height: 24px;
                    //   border-radius: 50%;
                    //   text-align: center;
                    //   margin-left: auto;
                    //   color: white;
                    //   border: none;
                    //   font-size: 18px;
                    //   background-color: #A8ABB5;
                    //   "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

                    //      }else{
                    var mnbtn = `<button id="equipmentplusbtn-${item.id}" onclick="addItemTo(${item.id})" style="width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      text-align: center;
                      margin-left: auto;
                      color: white;
                      border: none;
                      font-size: 18px;
                      background-color: #2FCC40;
                      "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

                    //  }
                    eqo += `
                <li class="menu-accordian mt-4" id="removeequipli-${item.id}">
                      <div class="row">
                          <div class="col-1" style="margin-top: 2px !important;">
${sfg}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3 text-end" style="padding-right: 0px;">
${mnbtn}
                          </div>
                      </div>
                  </li>

               `;

                });
                $('#equipmentdata').empty().append(eqo);
// console.info(response.hard_materials);
                var hardmaterials = '';
                $.each(response.hard_materials, function (index, item) {
                    if (item.is_favorite == 0) {
                        var sfg2 = `<svg width="16" id="nofavhard-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'hard_materials')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg2 = `<svg width="16" onclick="deleteItemToFavourite22(${item.id}, 'hard_materials')" id="yesfavhard-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
                    // console.info(item.is_exist);
//                         if(item.is_exist == 1) {

// var mnbtn2 = `<button disabled id="hardmplusbtn-${item.id}" onclick="addItemhardM(${item.id})" style="width: 24px;
// height: 24px;
// border-radius: 50%;
// text-align: center;
// margin-left: auto;
// color: white;
// border: none;
// font-size: 18px;
// background-color: #A8ABB5;
// "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

// }else{
                    var mnbtn2 = `<button id="hardmplusbtn-${item.id}" onclick="addItemMaterial(${item.id},'hard_materials')" style="width: 24px;
height: 24px;
border-radius: 50%;
text-align: center;
margin-left: auto;
color: white;
border: none;
font-size: 18px;
background-color: #2FCC40;
"><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

// }
                    hardmaterials += `
                <li class="menu-accordian mt-4" id="removehardli-${item.id}">
                      <div class="row">
                          <div class="col-1" style="margin-top: 2px !important;">
                             ${sfg2}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3 text-end" style="padding-right: 0px;">
                             ${mnbtn2}
                          </div>
                      </div>
                  </li>

               `;

                });
                $('#hardmaterials').empty().append(hardmaterials);
// console.info(response.plant_materials);
                var plantmaterials = '';
                $.each(response.plant_materials, function (index, item) {
                    if (item.is_favorite == 0) {
                        var sfg3 = `<svg width="16" id="nofavplant-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'plant_materials')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg3 = `<svg width="16" onclick="deleteItemToFavourite22(${item.id}, 'plant_materials')" id="yesfavplant-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }

//                         if(item.is_exist == 1) {

// var mnbtn=`<button disabled id="plantmplusbtn-${item.id}" onclick="addItemhardP(${item.id})" style="width: 24px;
// height: 24px;
// border-radius: 50%;
// text-align: center;
// margin-left: auto;
// color: white;
// border: none;
// font-size: 18px;
// background-color: #A8ABB5;
// "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

// }else{
                    var mnbtn = `<button id="plantmplusbtn-${item.id}" onclick="addItemMaterial(${item.id},'plant_materials')"" style="width: 24px;
height: 24px;
border-radius: 50%;
text-align: center;
margin-left: auto;
color: white;
border: none;
font-size: 18px;
background-color: #2FCC40;
"><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

// }
                    plantmaterials += `
                <li class="menu-accordian mt-4" id="removeplantli-${item.id}">
                      <div class="row">
                          <div class="col-1" style="margin-top: 2px !important;">
                              ${sfg3}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3 text-end" style="padding-right: 0px;">
                             ${mnbtn}
                          </div>
                      </div>
                  </li>

               `;

                });
                $('#plantmaterials').empty().append(plantmaterials);
                var othercost = '';
                $.each(response.other_job_costs, function (index, item) {
                    if (item.is_favorite == 0) {
                        var sfg4 = `<svg width="16" id="nofavother-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'other_costs')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg4 = `<svg width="16" onclick="deleteItemToFavourite22(${item.id}, 'other_costs')" id="yesfavother-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
//                         if(item.is_exist == 1) {

// var mnbtn=`<button disabled id="otherplusbtn-${item.id}" onclick="addItemCost(${item.id})" style="width: 24px;
// height: 24px;
// border-radius: 50%;
// text-align: center;
// margin-left: auto;
// color: white;
// border: none;
// font-size: 18px;
// background-color: #A8ABB5;
// "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

// }else{
                    var mnbtn = `<button id="otherplusbtn-${item.id}" onclick="addItemCost(${item.id})" style="width: 24px;
height: 24px;
border-radius: 50%;
text-align: center;
margin-left: auto;
color: white;
border: none;
font-size: 18px;
background-color: #2FCC40;
"><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

// }
                    othercost += `<li class="menu-accordian mt-4" id="removeotherli-${item.id}">
                      <div class="row">
                          <div class="col-1" style="margin-top: 2px !important;">
                              ${sfg4}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3 text-end" style="padding-right: 0px;">
                             ${mnbtn}
                          </div>
                      </div>
                  </li>

               `;

                });
                $('#othercost').empty().append(othercost);

                var labor = '';
                $.each(response.labors, function (index, item) {
                    if (item.is_favorite == 0) {
                        var sfg5 = `<svg width="16" id="nofavlabor-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'labors')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg5 = `<svg width="16" onclick="deleteItemToFavourite22(${item.id}, 'labors')" id="yesfavlabor-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
//                         if(item.is_exist == 1) {

// var mnbtn=`<button disabled id="laborplusbtn-${item.id}" onclick="addItemLabor(${item.id})" style="width: 24px;
// height: 24px;
// border-radius: 50%;
// text-align: center;
// margin-left: auto;
// color: white;
// border: none;
// font-size: 18px;
// background-color: #A8ABB5;
// "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

// }else{
                    var mnbtn = `<button id="laborplusbtn-${item.id}" onclick="addItemLabor(${item.id})" style="width: 24px;
height: 24px;
border-radius: 50%;
text-align: center;
margin-left: auto;
color: white;
border: none;
font-size: 18px;
background-color: #2FCC40;
"><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

// }
                    labor += `<li class="menu-accordian mt-4" id="removelaborsli-${item.id}">
                      <div class="row">
                          <div class="col-1" style="margin-top: 2px !important;">
                              ${sfg5}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3 text-end" style="padding-right: 0px;">
                              ${mnbtn}
                          </div>
                      </div>
                  </li>`;

                });
                $('#laborMenu').empty().append(labor);

                var contractor = '';
                $.each(response.sub_contractor, function (index, item) {
                    console.log(item.id);
                    if (item.is_favorite == 0) {
                        var sfg6 = `<svg width="16" id="nofavcontractor-${item.id}" height="14" onclick="addItemToFavourite(${item.id}, 'contractors')" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    } else if (item.is_favorite == 1) {
                        var sfg6 = `<svg width="16" onclick="deleteItemToFavourite22(${item.id}, 'contractors')" id="yesfavcontractor-${item.id}" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path stroke="red" class="equc-${item.id}" d="M10.7394 1C13.0876 1 14.6654 3.235 14.6654 5.32C14.6654 9.5425 8.11722 13 7.9987 13C7.88018 13 1.33203 9.5425 1.33203 5.32C1.33203 3.235 2.90981 1 5.25796 1C6.60611 1 7.48759 1.6825 7.9987 2.2825C8.50981 1.6825 9.39129 1 10.7394 1Z" stroke="#D3D6E0" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>`;
                    }
//                         if(item.is_exist == 1) {

// var mnbtn=`<button disabled id="contractorplusbtn-${item.id}" onclick="addItemContractor(${item.id})" style="width: 24px;
// height: 24px;
// border-radius: 50%;
// text-align: center;
// margin-left: auto;
// color: white;
// border: none;
// font-size: 18px;
// background-color: #A8ABB5;
// "><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

// }else{
                    var mnbtn = `<button id="contractorplusbtn-${item.id}" onclick="addItemContractor(${item.id})" style="width: 24px;
height: 24px;
border-radius: 50%;
text-align: center;
margin-left: auto;
color: white;
border: none;
font-size: 18px;
background-color: #2FCC40;
"><label for=""><b style="color: white; font-size: 17px;">+</b></label></button>`;

// }
                    contractor += `<li class="menu-accordian mt-4" id="removecontractorli-${item.id}">
                      <div class="row">
                          <div class="col-1" style="margin-top: 2px !important;">
                              ${sfg6}
                          </div>
                          <div class="col-7 text-left">
                              <span>${item.name}</span>
                          </div>
                          <div id="${item.id}" class="col-3 text-end" style="padding-right: 0px;">
                             ${mnbtn}
                          </div>
                      </div>
                  </li>`;

                });
                $('#sub_contractor_list').empty().append(contractor);
                // createAccordion(response);
                // $('.showMessage').html(`
                // <div class="alert alert-success alert-dismissible fade show showMessage">
                //     File imported successfully!.
                //     </div>

                // `);
                // materials_table.draw();

            },
            error: function (response) {

            }
        });
    }
</script>
<script>
    // new code for add new item start here
    async function addItemTo(itemId) {
        const sectionId = getSelectedSectionId();
        const url = "{{ URL::route(getRouteAlias() . '.division.getDataid') }}";
        try {

            const response = await fetch(`${url}?id=${itemId}`);
            const result = await response.json();
            const equipment = result.equipment;
            const margin = result.margins;

            if (!margin?.default) {
                $('#grossmarginmodel').modal('show');
                return;
            }

            const quantity = 1;
            const unitCost = parseFloat(equipment.cost);
            const grossMargin = parseFloat(margin.default);
            const revenue = calculateItemRevenue(unitCost, grossMargin);
            const unitPrice = revenue; // the revenue give the total unit price with gross margin
            const totalCost = quantity * unitCost;
            const totalPrice = quantity * unitPrice;
            /*alert(JSON.stringify({
                unitCost: unitCost,
                grossMargin: grossMargin,
                revenue: revenue,
                unitPrice: unitPrice,
                totalCost: totalCost,
                totalPrice: totalPrice
            }, null, 2));*/

            updateGrossMarginSummaryItem(revenue);
            updateEquipmentCostTotals(totalCost, totalPrice);

            saveBatchItems('equipment', equipment?.id, quantity, equipment.uom, totalCost, grossMargin, unitCost, unitPrice, totalPrice, sectionId,'','', function(savedId) {
                const newRowHTML = buildEquipmentRow(savedId, equipment, margin, quantity, unitCost, totalCost, revenue, unitPrice, totalPrice);
                appendRowToDOMItem(newRowHTML, sectionId);
                calculateTotalCostForAllItems(); // update the total price of the opportunity
            });

        } catch (error) {
            console.error("Error fetching equipment data:", error);
            alert("Failed to load equipment. Please try again.");
        }
    }

    function getSelectedSectionId() {
        const checkedSection = document.querySelector('.checked-class');
        return checkedSection ? checkedSection.dataset.id : '';
    }

    function calculateItemRevenue(cost, margin) {
        return cost / (1 - (margin / 100));
    }

    function updateGrossMarginSummaryItem(currentRevenue) {
        const marginInputs = document.querySelectorAll('.sumofmargin');
        let totalRevenue = currentRevenue;

        marginInputs.forEach(input => {
            totalRevenue += parseFloat(input.value) || 0;
        });

        document.getElementById('totalaveragegrossmargen').textContent = totalRevenue.toFixed(2);
    }

    function updateEquipmentCostTotals(newCost, newPrice) {
        const currentCost = sumValuesByClass('equipmentcount');
        const updatedCost = parseFloat(newCost) + parseFloat(currentCost);
        document.getElementById('totalcountequip').textContent = `$${updatedCost.toFixed(2)}`;

        const currentPrice = sumValuesByClass('equipmenttotalprice');
        const updatedPrice = parseFloat(newPrice) + parseFloat(currentPrice);
        document.getElementById('totalcountequipPrice').textContent = `$${updatedPrice.toFixed(2)}`;
    }

    function sumValuesByClass(className) {
        const elements = document.querySelectorAll(`.${className}`);
        let sum = 0;
        elements.forEach(el => {
            sum += parseFloat(el.textContent) || 0;
        });
        return sum;
    }

    function buildEquipmentRow(id, data, margin, quantity, unitCost, totalCost, revenue, unitPrice, totalPrice) {
        id=id?.id;
        return `
        <tr class="border datarowsequ-${id}">
            <td class="drag-handle">
               <img src="/asset/assets/images/drag-drop-icon.svg" alt="drag and drop icon">
            </td>
            <td class="border">
                <label>${data.name}</label>
                <input type="text" style="display: none;" class="getitemsequipid-${id}">
            </td>
            <td></td>
            <td class="border" colspan="1">
                <input type="number"
                    oninput="changeQuantityVal(${id}, 'equipment', '${data.uom}', '', 'quantityequ', 'grossmarginequ', 'unitcostsequ', 'totalcostsequip', 'totalpriceequi'); if(this.value < 1) this.value = 1;"
                    value="${quantity}" min="1"
                    style="width: 100%;" id="quantityequ-${id}" placeholder="01" />
            </td>
            <td class="border">${data.uom}</td>
            <td class="border" id="unitcostsequ-${id}">$${unitCost.toFixed(2)}</td>
            <td class="totalCostOfItem border equipmentcount" id="totalcostsequip-${id}">$${totalCost.toFixed(2)}</td>
            <td class="border">
                <input type="text" style="display: none" id="sumofmarginequ-${id}" value="$${revenue.toFixed(2)}" class="sumofmargin" />
                <input type="hidden"
                    oninput="changeQuantityVal(${id}, 'equipment', '${data.uom}', '', 'quantityequ', 'grossmarginequ', 'unitcostsequ', 'totalcostsequip', 'totalpriceequi'); if(this.value < ${margin.minimum}) this.value = ${margin.minimum};"
                    id="grossmarginequ-${id}" value="${margin.default}" />
                ${margin.default}
            </td>
            <td class="totalPriceOfItem border equipmenttotalprice" id="totalpriceequi-${id}">$${totalPrice.toFixed(2)}</td>
            <td class="border" id="unitprice-${id}">$${unitPrice.toFixed(2)}</td>
            <td class="border" style="text-align: center;">
                <a href="javascrit:void(0)" onclick="deletedatarowsequ2(${id}, '${totalCost}')">
                    <img src="/asset/assets/images/trash-icon.svg" alt="trash icon">
                </a>
            </td>
        </tr>
    `;
    }

    function appendRowToDOMItem(rowHTML, sectionId) {
        if (sectionId) {
            document.querySelector(`.accord-bodys-${sectionId}`).insertAdjacentHTML('beforeend', rowHTML);
        } else {
            document.getElementById('sortable-table-body').insertAdjacentHTML('beforeend', rowHTML);
        }
    }
    // new code for add new item end here

    document.getElementById("sqft").addEventListener("input", function () {
        if (this.value < 0) {
            this.value = "";
        }
    });

    document.getElementById("depth").addEventListener("input", function () {
        if (this.value < 0) {
            this.value = "";
        }
    });

    function submitInput() {
        let id = $('#hard_material_id').val();
        let uom = $('#hard_material_uom').val();

        let sqft = $('#sqftVal').val();
        let depth = $("#depthVal").val();
        const baseUrl = "{{ route(getRouteAlias() . '.estimate.hard-item-update') }}";
        $.ajax({
            url: baseUrl,
            method: 'POST',
            data: {
                depth: depth,
                sqft: sqft,
                id: id,
                _token: '{{ csrf_token() }}'
            },
            success: function (response) {
                if(response) {
                    //toastr.success(response.message);
                    /*$('#editItemModal').modal('hide');
                    window.location.reload();*/
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error("Error saving batch items:", textStatus, errorThrown);
                console.info("All items saved successfully: Error");
            }
        });


        $('#depth-'+id).val(depth);
        $('#sqft-'+id).val(sqft);
        changeDepth(id,uom);
        let modalElement = document.getElementById("inputModal");
        let modalInstance = bootstrap.Modal.getInstance(modalElement); // Get the modal instance
        if (modalInstance) {
            modalInstance.hide(); // Properly hide the modal
        }
        window.location.reload();
    }


    async function addItemMaterial(itemId, type) {
        const isHardMaterial = type === 'hard_materials';
        const url = isHardMaterial
            ? "{{ URL::route(getRouteAlias() . '.division.getDataHard') }}"
            : "{{ URL::route(getRouteAlias() . '.division.getDataPlant') }}";

        try {
            const response = await fetch(`${url}?id=${itemId}`);
            const { equipment: data, margins: margin } = await response.json();

            if (!margin?.default) {
                $('#grossmarginmodel').modal('show');
                return;
            }

            const quantity = 1;
            const unitCost = parseFloat(data.cost);
            const grossMargin = parseFloat(margin.default);
            const revenue = calculateRevenue(unitCost, grossMargin);
            const unitPrice = revenue;
            const totalCost = quantity * unitCost;
            const totalPrice = quantity * unitPrice;

            // ⭐️ Labor hours calculation
            const laborPerUnit = parseFloat(isHardMaterial ? (data.labor ?? 0) : (data?.install ?? 0)) || 0;
            const laborHours = laborPerUnit * quantity;
            updateTotalLaborHours(laborHours); // <-- Function to add to your code

            updateMaterialCostTotals(totalCost, totalPrice);
            updateGrossMarginSummary(revenue);

            const sectionId = getSelectedSectionId();
            const uom = isHardMaterial ? data.uom : data.size;

            saveBatchItems(type, itemId, quantity, uom, totalCost, grossMargin, unitCost, unitPrice, totalPrice, sectionId, '', '', function (savedId) {
                const rowHTML = isHardMaterial
                    ? buildHardMaterialRow(savedId, data, margin, quantity, unitCost, totalCost, revenue, unitPrice, totalPrice)
                    : buildPlantMaterialRow(savedId, data, margin, quantity, unitCost, totalCost, revenue, unitPrice, totalPrice);

                appendRowToDOM(rowHTML, sectionId);
                calculateTotalCostForAllItems(); // update the total price of the opportunity
                // addItemToTemp(type, itemId, quantity, uom, totalCost, grossMargin);
                calculateTotalCostForAllItems(); // update the total price of the opportunity
                if(isHardMaterial) {
                    $('#inputModal').modal('show');
                }
            });

        } catch (error) {
            console.error(`Error loading ${type}:`, error);
            alert("Failed to load material data. Please try again.");
        }
    }

    function updateMaterialCostTotals(newCost, newPrice) {
        const currentCost = sumValuesByClass('materialcount');
        const currentPrice = sumValuesByClass('materialtotalprice');
        $('#totalcountmaterial').text(`$${(currentCost + newCost).toFixed(2)}`);
        $('#totalcountmaterialPrice').text(`$${(currentPrice + newPrice).toFixed(2)}`);
    }

    function updateGrossMarginSummary(revenue) {
        let sum = 0;
        document.querySelectorAll('.sumofmargin').forEach(input => {
            sum += parseFloat(input.value) || 0;
        });
        $('#totalaveragegrossmargen').text((sum + revenue).toFixed(2));
    }

    function appendRowToDOM(rowHTML, sectionId) {
        if (sectionId) {
            $(`.accord-bodys-${sectionId}`).append(rowHTML);
        } else {
            $('#sortable-table-body').append(rowHTML);
        }
    }

    function calculateRevenue(unitCost, grossMargin) {
        return unitCost / (1 - (grossMargin / 100));
    }

    function buildHardMaterialRow(id, data, margin, quantity, unitCost, totalCost, revenue, unitPrice, totalPrice) {
        id = id?.id;
        return `
                <tr class="border datarowshard-${id}" data-item-id="${id}">
                    <td class="drag-handle"><img src="/asset/assets/images/drag-drop-icon.svg" alt="drag and drop"></td>
                    <td class="border">
                        <label>${data.name}</label>
                        <input type="hidden" id="depth-${id}" oninput="changeDepth(${id}, '${data.uom}')">
                        <input type="hidden" id="calculatedQty-${id}">
                        <input type="hidden" id="sqft-${id}" oninput="changeDepth(${id}, '${data.uom}')">
                        <input type="text" style="display: none;" class="getitemshardid-${id}">
                    </td>
                    <td class="border">
                        <span class="getDetailsOfItem" data-id="${data.id}" style="cursor:pointer;">
                            <img src="/asset/assets/images/icons8-cube-64.png" alt="cube" style="width: 24px; height: 24px;">
                        </span>
                    </td>
                    <td class="border">
                        <input type="number" style="width: 100%;" id="quantityhar-${id}" value="${quantity}" min="1"
                            oninput="changeQuantityVal(${id}, 'hard_materials', '${data.uom}', '', 'quantityhar', 'grossmarginhar', 'unitcostshar', 'totalcostshard', 'totalpricehardm'); if(this.value < 1) this.value = 1;" />
                    </td>
                    <td class="border">${data.uom}</td>
                    <td class="border" id="unitcostshar-${id}">${unitCost.toFixed(2)}</td>
                    <td class="totalCostOfItem border materialcount" id="totalcostshard-${id}">${totalCost.toFixed(2)}</td>
                    <td class="border">
                        <input type="hidden" id="sumofmarginhar-${id}" class="sumofmargin" value="${revenue.toFixed(2)}" />
                        <input type="hidden" id="grossmarginhar-${id}" value="${margin.default}"
                            oninput="changeQuantityVal(${id}, 'hard_materials', '${data.uom}', '', 'quantityhar', 'grossmarginhar', 'unitcostshar', 'totalcostshard', 'totalpricehardm'); if(this.value < ${margin.minimum}) this.value = ${margin.minimum};" />
                        ${margin.default}
                    </td>
                    <td class="totalPriceOfItem border materialtotalprice" id="totalpricehardm-${id}">${totalPrice.toFixed(2)}</td>
                    <td class="border" id="unitprice-${id}">${unitPrice.toFixed(2)}</td>
                    <td class="border" style="text-align: center;">
                        <a href="javascript:void(0)" onclick="deletedatarowsequ2(${id}, '', '', '')">
                            <img src="/asset/assets/images/trash-icon.svg" alt="delete">
                        </a>
                    </td>
                </tr>`;
    }

    function buildPlantMaterialRow(id, data, margin, quantity, unitCost, totalCost, revenue, unitPrice, totalPrice) {
        id = id?.id;
        return `
                <tr class="border datarowsplant-${id}" data-item-id="${id}">
                    <td class="drag-handle"><img src="/asset/assets/images/drag-drop-icon.svg" alt="drag and drop"></td>
                    <td class="border">
                        <label>${data.name}</label>
                        <input type="text" style="display: none;" class="getitemsplantid-${id}">
                    </td>
                    <td></td>
                    <td class="border">
                        <input type="number" style="width: 100%;" value="${quantity}" min="1"
                            id="quantitypla-${id}"
                            oninput="changeQuantityVal(${id}, 'plant_materials', '${data.size}', '', 'quantitypla', 'grossmarginpla', 'unitcostspla', 'totalcostsplant', 'totalpriceplantm'); if(this.value < 1) this.value = 1;" />
                    </td>
                    <td class="border">${data.size}</td>
                    <td class="border" id="unitcostspla-${id}">${unitCost.toFixed(2)}</td>
                    <td class="totalCostOfItem border materialcount" id="totalcostsplant-${id}">${totalCost.toFixed(2)}</td>
                    <td class="border">
                        <input type="text" style="display: none;" value="${revenue.toFixed(2)}" id="sumofmarginpla-${id}" class="sumofmargin"/>
                        <input type="hidden" id="grossmarginpla-${id}" value="${margin.default}"
                            oninput="changeQuantityVal(${id}, 'plant_materials', '${data.size}', '', 'quantitypla', 'grossmarginpla', 'unitcostspla', 'totalcostsplant', 'totalpriceplantm'); if(this.value < ${margin.minimum}) this.value = ${margin.minimum};" />
                        ${margin.default}
                    </td>
                    <td class="totalPriceOfItem border materialtotalprice" id="totalpriceplantm-${id}">${totalPrice.toFixed(2)}</td>
                    <td class="border" id="unitprice-${id}">${unitPrice.toFixed(2)}</td>
                    <td class="border" style="text-align: center;">
                        <a href="javascript:void(0)" onclick="deletedatarowsequ2(${id}, '', '', '')">
                            <img src="/asset/assets/images/trash-icon.svg" alt="delete">
                        </a>
                    </td>
                </tr>`;
    }

    function updateTotalLaborHours(newHours) {
        const currentHours = parseFloat($('#totalhourslabors').text()) || 0;
        const updatedHours = currentHours + newHours;
        $('#totalhourslabors').text(updatedHours.toFixed(2));
    }

    async function addItemCost(itemId) {
        const sectionId = getSelectedSectionId();
        const url = "{{ URL::route(getRouteAlias() . '.division.getDataCost') }}";

        try {
            const response = await fetch(`${url}?id=${itemId}`);
            const result = await response.json();

            const data = result.equipment;
            const margin = result.margins;

            if (!margin?.default) {
                $('#grossmarginmodel').modal('show');
                return;
            }

            const quantity = 1;
            const unitCost = parseFloat(data.cost);
            const grossMargin = parseFloat(margin.default);
            const revenue = calculateRevenue(unitCost, grossMargin);
            const unitPrice = revenue;
            const totalCost = quantity * unitCost;
            const totalPrice = quantity * unitPrice;

            updateGrossMarginSummary(revenue);
            updateOtherCostTotals(totalCost, totalPrice);

            saveBatchItems('other_costs', data?.id, quantity, data.uom, totalCost, grossMargin, unitCost, unitPrice, totalPrice, sectionId, '', '', function (savedId) {
                const rowHTML = buildOtherCostRow(savedId, data, margin, quantity, unitCost, totalCost, revenue, unitPrice, totalPrice);
                appendRowToDOM(rowHTML, sectionId);
                calculateTotalCostForAllItems(); // update the total price of the opportunity
            });

            addItemToTemp("other_costs", itemId, quantity, data.uom, totalCost, grossMargin);

        } catch (error) {
            console.error("Error fetching other cost data:", error);
            alert("Failed to load other cost. Please try again.");
        }
    }

    function updateOtherCostTotals(newCost, newPrice) {
        const currentCost = sumValuesByClass('othercostcount');
        const updatedCost = parseFloat(newCost) + parseFloat(currentCost);
        document.getElementById('totalcountother').textContent = `$${updatedCost.toFixed(2)}`;

        const currentPrice = sumValuesByClass('othertotalprice');
        const updatedPrice = parseFloat(newPrice) + parseFloat(currentPrice);
        document.getElementById('totalcountotherPrice').textContent = `$${updatedPrice.toFixed(2)}`;
    }

    function buildOtherCostRow(id, data, margin, quantity, unitCost, totalCost, revenue, unitPrice, totalPrice) {
        id = id?.id;
        return `
    <tr class="border datarowsother-${id}" data-item-id="${id}">
        <td class="drag-handle">
            <img src="/asset/assets/images/drag-drop-icon.svg" alt="drag and drop icon">
        </td>
        <td class="border">
            <label>${data.name}</label>
            <input type="text" style="display: none;" class="getitemsotherid-${id}">
        </td>
        <td></td>
        <td class="border">
            <input type="number" value="${quantity}" min="1"
                id="quantitycos-${id}" style="width: 100%;"
                oninput="changeQuantityVal(${id}, 'other_costs', '${data.uom}', '', 'quantitycos', 'grossmargincos', 'unitcostscos', 'totalcostscosts', 'totalpriceothercost'); if(this.value < 1) this.value = 1;" />
        </td>
        <td class="border">${data.uom}</td>
        <td class="border" id="unitcostscos-${id}">${unitCost.toFixed(2)}</td>
        <td class="totalCostOfItem border othercostcount" id="totalcostscosts-${id}">${totalCost.toFixed(2)}</td>
        <td class="border">
            <input type="hidden" id="sumofmarginoth-${id}" class="sumofmargin" value="${revenue.toFixed(2)}" />
            <input type="hidden" id="grossmargincos-${id}" value="${margin.default}"
                oninput="changeQuantityVal(${id}, 'other_costs', '${data.uom}', '', 'quantitycos', 'grossmargincos', 'unitcostscos', 'totalcostscosts', 'totalpriceothercost'); if(this.value < ${margin.minimum}) this.value = ${margin.minimum};" />
            ${margin.default}
        </td>
        <td class="totalPriceOfItem border othertotalprice" id="totalpriceothercost-${id}">${totalPrice.toFixed(2)}</td>
        <td class="border" id="unitprice-${id}">${unitPrice.toFixed(2)}</td>
        <td class="border" style="text-align: center;">
            <a href="javascript:void(0)" onclick="deletedatarowsequ2(${id}, '', '', '')">
                <img src="/asset/assets/images/trash-icon.svg" alt="trash icon">
            </a>
        </td>
    </tr>
    `;
    }

    async function addItemLabor(itemId) {
        const sectionId = getSelectedSectionId();
        const url = "{{ URL::route(getRouteAlias() . '.division.getDataLabor') }}";

        try {
            const response = await fetch(`${url}?id=${itemId}`);
            const result = await response.json();

            const data = result.equipment;
            const margin = result.margins;

            if (!margin?.default) {
                $('#grossmarginmodel').modal('show');
                return;
            }

            const quantity = 1;
            const unitCost = parseFloat(data.cost);
            const grossMargin = parseFloat(margin.default);
            const revenue = calculateRevenue(unitCost, grossMargin);
            const unitPrice = revenue; // revnue has sum of unit cost and gross margin
            const totalCost = quantity * unitCost;
            const totalPrice = quantity * unitPrice;
            const laborBurden = parseFloat(data.labor_burden) || 0;
            const totalBurden = totalPrice * (laborBurden / 100);

            updateGrossMarginSummary(revenue);
            updateLaborTotals(totalCost, totalPrice, totalBurden, data.name);

            saveBatchItems('labors', data?.id, quantity, data.uom, totalCost, grossMargin, unitCost, unitPrice, totalPrice, sectionId, '', '', function(savedId) {
                const rowHTML = buildLaborRow(savedId, data, margin, quantity, unitCost, totalCost, revenue, unitPrice, totalPrice, totalBurden);
                appendRowToDOM(rowHTML, sectionId);
                calculateTotalCostForAllItems(); // update the total price of the opportunity
            });

            addItemToTemp("labors", itemId, quantity, data.uom, totalCost, grossMargin);

        } catch (error) {
            console.error("Error fetching labor data:", error);
            alert("Failed to load labor data. Please try again.");
        }
    }

    function updateLaborTotals(newCost, newPrice, newBurden, laborType) {
        const isLaborer = laborType === 'Laborers';
        const selector = isLaborer ? '#totalhourslabors' : '#totalhourssupervision';
        const className = isLaborer ? 'totalhourslabors' : 'totalhourssupervisions';

        const prevHours = parseFloat($(selector).text()) || 0;
        $(selector).text(prevHours + 1);

        const currentCost = sumValuesByClass('laborcount');
        const currentPrice = sumValuesByClass('labortotalprice');
        const currentBurden = sumValuesByClass('burdencount');

        document.getElementById('totalcountlabor').textContent = `$${(newCost + currentCost).toFixed(2)}`;
        document.getElementById('totalcountlaborPrice').textContent = `$${(newPrice + currentPrice).toFixed(2)}`;
        document.getElementById('totalcountburden').textContent = `$${(newBurden + currentBurden).toFixed(2)}`;
    }

    function buildLaborRow(id, data, margin, quantity, unitCost, totalCost, revenue, unitPrice, totalPrice, burden) {
        id = id?.id;
        const isLaborer = data.name === 'Laborers';
        const hourClass = isLaborer ? 'totalhourslabors' : 'totalhourssupervisions';

        return `
    <tr class="border datarowsLabor-${id}" data-item-id="${id}">
        <td class="drag-handle">
            <p class="burdencount d-none" >${burden.toFixed(2)}</p>
            <img src="/asset/assets/images/drag-drop-icon.svg" alt="drag and drop icon">
        </td>
        <td class="border">
            <input class="items-input" type="text" id="laboritemname-${id}" oninput="changelaboritemname(${id})" value="${data.name}" />
            <input type="hidden" value="${data.id}" class="getitemsid-${id}">
        </td>
        <td></td>
        <td class="border">
            <input type="number"
                class="${hourClass}" data-value="${data.name}" value="1" min="1"
                id="quantitylabo-${id}" style="width: 100%;"
                oninput="changeQuantityVal(${id}, 'labors','${data.uom}', '${data.labor_burden}', 'quantitylabo', 'grossmarginlabo', 'unitcostslabo', 'totalcostslabo', 'totalpricelaborm', '${data.name}'); if(this.value < 1) this.value = 1;" />
        </td>
        <td class="border">${data.uom}</td>
        <td class="border" id="unitcostslabo-${id}">${unitCost.toFixed(2)}</td>
        <td class="totalCostOfItem border laborcount" id="totalcostslabo-${id}">${totalCost.toFixed(2)}</td>
        <td class="border">
            <input type="hidden" id="sumofmarginlab-${id}" value="${revenue.toFixed(2)}" class="sumofmargin" />
            <input type="hidden" id="grossmarginlabo-${id}" value="${margin.default}" />
            ${margin.default}
        </td>
        <td class="totalPriceOfItem border labortotalprice" id="totalpricelaborm-${id}">${totalPrice.toFixed(2)}</td>
        <td class="border" id="unitprice-${id}">${unitPrice}</td>
        <td class="border" style="text-align: center;">
            <a href="javascript:void(0)" onclick="deletedatarowsequ2(${id}, '', '', '')">
                <img src="/asset/assets/images/trash-icon.svg" alt="trash icon">
            </a>
        </td>
    </tr>
    `;
    }

    function changelaboritemname2(id) {
        var name = $("#laboritemname-" + id).val();
        console.info(name);
        $.ajax({
            url: "{{route('organization.update.estimate.item.name')}}", // Backend route
            type: 'POST',
            data: {
                id: id,
                name: name,
                _token: $('meta[name="csrf-token"]').attr('content') // CSRF token for security
            },
            success: function (response) {
                if (response.success) {
                    // alert('Item name updated successfully!');
                } else {
                    alert('Failed to update item name.');
                }
            },
            error: function (xhr) {
                console.error('Error:', xhr.responseText);
            }
        });
    }

    const changelaboritemname = debounce(changelaboritemname2, 1000);

    function changecontractoritemname2(id) {
        var name = $("#contractoritemname-" + id).val();
        console.info(name);
        $.ajax({
            url: "{{route('organization.update.estimate.item.name')}}", // Backend route
            type: 'POST',
            data: {
                id: id,
                name: name,
                _token: $('meta[name="csrf-token"]').attr('content') // CSRF token for security
            },
            success: function (response) {
                if (response.success) {
                    // alert('Item name updated successfully!');
                } else {
                    alert('Failed to update item name.');
                }
            },
            error: function (xhr) {
                console.error('Error:', xhr.responseText);
            }
        });
    }

    const changecontractoritemname = debounce(changecontractoritemname2, 1000);

    async function addItemContractor(contractorId) {
        const url = "{{ URL::route(getRouteAlias() . '.division.getDataContractor') }}";

        try {
            const response = await $.get(url, { id: contractorId });
            const data = response.equipment;
            const margin = response.margins;

            if (!margin?.default) {
                $('#grossmarginmodel').modal('show');
                return;
            }

            const quantity = 1;
            const unitCost = parseFloat(data.cost);
            const grossMargin = parseFloat(margin.default);
            const totalCost = quantity * unitCost;
            const totalRevenue = unitCost * (1 + (grossMargin / 100));
            const unitPrice =  totalRevenue;
            const totalPrice = unitPrice * quantity;

            // Update gross margin summary
            updateGrossMarginTotal(totalRevenue);

            // Update total contractor values
            const currentSubTotal = sumEquipmentCount('subcontcount');
            const newSubTotal = currentSubTotal + totalCost;
            $('#totalSubContractorsValu').text(`$${newSubTotal.toFixed(2)}`);

            const currentPriceTotal = sumEquipmentCount('contractortotalprice');
            const newPriceTotal = currentPriceTotal + totalPrice;
            $('#totalSubContractorsValuPrice').text(`$${newPriceTotal.toFixed(2)}`);

            // Determine section name if selected
            const sectionName = $('.checked-class')?.data('id') || '';

            // Save batch item
            saveBatchItems("contractors", contractorId, quantity, data.uom, totalCost, grossMargin, unitCost, unitPrice, totalPrice, sectionName, '', '', function (savedId) {
                const rowHtml = buildContractorRow(savedId, data.name, quantity, data.uom, unitCost, totalCost, grossMargin, totalRevenue, unitPrice, totalPrice, margin.minimum);

                if (sectionName) {
                    $(`.accord-bodys-${sectionName}`).append(rowHtml);
                } else {
                    $('#sortable-table-body').append(rowHtml);
                }
                calculateTotalCostForAllItems(); // update the total price of the opportunity
                addItemToTemp("contractors", contractorId, quantity, data.uom, totalCost, grossMargin);
            });

        } catch (error) {
            console.error("Error loading contractor data:", error);
            alert("Failed to load contractor data. Please try again.");
        }
    }

    function updateGrossMarginTotal(additionalMargin) {
        let total = 0;
        document.querySelectorAll('.sumofmargin').forEach(input => {
            total += parseFloat(input.value) || 0;
        });
        $('#totalaveragegrossmargen').text((total + additionalMargin).toFixed(2));
    }

    function buildContractorRow(id, name, quantity, uom, unitCost, totalCost, grossMargin, revenue, unitPrice, totalPrice, minMargin) {
        id = id?.id;
        return `
                <tr class="border datarowsContractor-${id}" data-item-id="${id}">
                    <td class="drag-handle"><img src="/asset/assets/images/drag-drop-icon.svg" alt="drag and drop"></td>
                    <td class="border">
                        <input type="text" class="items-input" id="contractoritemname-${id}" value="${name}" oninput="changecontractoritemname(${id})" />
                        <input type="hidden" class="getitemscontractorid-${id}">
                    </td>
                    <td></td>
                    <td class="border">
                        <input class="totalhourquantity" type="number" style="width: 100%;" id="quantitycontss-${id}" value="${quantity}" min="1"
                               oninput="changeQuantityVal(${id}, 'contractors', '${uom}', '', 'quantitycontss', 'grossmargincont', 'unitcostscont', 'totalcostscontr', 'totalpricecontractorm'); if(this.value < 1) this.value = 1;" />
                    </td>
                    <td class="border">${uom}</td>
                    <td class="border">
                        <input type="number" id="unitcostscont-${id}" value="${unitCost}" min="1" style="width: 100%;"
                               oninput="changeQuantityVal(${id}, 'contractors', '${uom}', '', 'quantitycontss', 'grossmargincont', 'unitcostscont', 'totalcostscontr', 'totalpricecontractorm'); if(this.value < 1) this.value = 1;" />
                    </td>
                    <td class="totalCostOfItem border subcontcount" id="totalcostscontr-${id}">${totalCost.toFixed(2)}</td>
                    <td class="border">
                        <input type="hidden" value="${revenue.toFixed(2)}" id="sumofmargincon-${id}" class="sumofmargin"/>
                        <input type="hidden" value="${grossMargin}" id="grossmargincont-${id}"
                               oninput="changeQuantityVal(${id}, 'contractors', '${uom}', '', 'quantitycontss', 'grossmargincont', 'unitcostscont', 'totalcostscontr', 'totalpricecontractorm'); if(this.value < ${minMargin}) this.value = ${minMargin};" />
                        ${grossMargin}
                    </td>
                    <td class="totalPriceOfItem border contractortotalprice" id="totalpricecontractorm-${id}">${totalPrice.toFixed(2)}</td>
                    <td class="border" id="unitprice-${id}">${unitPrice.toFixed(2)}</td>
                    <td class="border" style="text-align: center;">
                        <a href="javascript:void(0)" onclick="deletedatarowsequ2(${id}, '', '', '')">
                            <img src="/asset/assets/images/trash-icon.svg" alt="delete">
                        </a>
                    </td>
                </tr>`;
    }

    function calculateTotalCostForAllItems() {
        let sum = 0;
        document.querySelectorAll('.totalPriceOfItem').forEach(el => {
            const totalPrice = el.textContent.replace('$', '').trim();
            sum += parseFloat(totalPrice) || 0;
        });
        $('#totalPriceCount').text('$'+sum);
    }

</script>
<script>
    $(document).ready(function () {
        // Handle click event on the edit icon
        $('#edit-icon').on('click', function () {
            // Hide the text elements and show the input fields
            $('#start-time-text').hide();
            $('#end-time-text').hide();
            $('#terms-text').hide();

            $('#start-time-input').show();
            $('#end-time-input').show();
            $('#terms-input').show();

            // Hide the edit icon
            $(this).hide();

            // Show the save button
            if (!$('#save-btn').length) {
                $(this).after('<button id="save-btn" class="btn btn-primary">Save</button>');
            }
        });

        // Handle click event on the save button
        $(document).on('click', '#save-btn', function () {
            // Get new values from the input fields
            var startTime = $('#start-time-input').val();
            var endTime = $('#end-time-input').val();
            var terms = $('#terms-input').val();

            // Update the text elements with the new values
            $('#start-time-text').text(startTime !== '' ? startTime : '---');
            $('#end-time-text').text(endTime !== '' ? endTime : '---');
            $('#terms-text').text(terms !== '' ? terms : '---');

            // Show the text elements and hide the input fields
            $('#start-time-text').show();
            $('#end-time-text').show();
            $('#terms-text').show();

            $('#start-time-input').hide();
            $('#end-time-input').hide();
            $('#terms-input').hide();
            var csrfToken = $('meta[name="csrf-token"]').attr('content');

            $(this).remove();
            $('#edit-icon').show();

            var opportunityId = $('#opportunity-id').val();

            $.ajax({
                url: "{{ route('organization.proposals.store') }}",
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                },
                data: {
                    opportunity_id: opportunityId,
                    contract_start_date: startTime,
                    contract_end_date: endTime,
                    contract_terms_months: terms,
                },
                success: function (response) {
                    toastr.success(response);
                },
                error: function (response) {
                    // Handle error response
                    toastr.warning(response);
                }
            });
        });
    });
    // Assuming you have jQuery included
    $(document).ready(function () {
        $('.dropdown-menu a').click(function (e) {
            // e.preventDefault();
            var selectedValue = $(this).data('value');
            $('#selected-probability').text(selectedValue + '%');
            // You can also update the value in your form or perform other actions here
        });
    });
    $(document).ready(function () {
        $('#statusDropdown').on('change', function () {
            var status = $(this).val();
            var opportunityId = $(this).data('id');
            var csrfToken = $('meta[name="csrf-token"]').attr('content');
            var url = "{{ route('organization.opportunitiesUpdateStatus', ['id' => ':id']) }}";
            url = url.replace(':id', opportunityId);
            $.ajax({
                url: url, // Replace with your actual URL endpoint
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                },
                data: {status: status},
                success: function (data) {
                    toastr.success(data.message);
                    applyStatusColor(status);
                    location.reload();

                },
                error: function (error) {
                    toastr.warning(error);
                }
            });
        });

        function applyStatusColor(status) {
            var $select = $('#statusDropdown');
            var styles = getStatusStyles(status, status);

            // Apply styles to the select element
            $select.css(styles);

            // Apply styles to individual options
            $select.find('option').each(function () {
                var optionStatus = $(this).val();
                var optionStyles = getStatusStyles(optionStatus, status);
                $(this).css(optionStyles);
            });
        }

// Apply the initial color based on the current status
        $(document).ready(function () {
            var initialStatus = $('#statusDropdown').val();
            applyStatusColor(initialStatus);
        }); // Call the color application function here
    });

    function getStatusStyles(status, currentStatus) {
        var originalStyles = {
            1: {color: 'rgba(239, 141, 3, 1)', backgroundColor: 'rgba(255, 245, 233, 1)'},
            2: {color: 'rgba(0, 116, 217, 1)', backgroundColor: 'rgba(230, 241, 251, 1)'},
            3: {color: 'rgba(117, 82, 255, 1)', backgroundColor: 'rgba(234, 229, 251, 1)'},
            4: {color: 'rgba(159, 107, 39, 1)', backgroundColor: 'rgba(239, 238, 235, 1)'},
            5: {color: 'rgba(10, 196, 152, 1)', backgroundColor: 'rgba(210, 245, 237, 1)'},
            6: {color: 'rgba(81, 86, 108, 1)', backgroundColor: 'rgba(226, 228, 234, 1)'},
        };

        var defaultStyle = {color: 'rgba(81, 86, 108, 1)', backgroundColor: 'rgba(226, 228, 234, 1)'};

        if (currentStatus <= status) {
            return originalStyles[currentStatus];
        }

        return defaultStyle;
    }

    function setDropdownText(element) {
        const dropdownButton = document.getElementById('dropdownMenuButton');
        dropdownButton.textContent = element.textContent;
    }

    document.addEventListener('DOMContentLoaded', function () {
        document.getElementById('bulk-download-icon')?.addEventListener('click', function () {
            // Select all image download links
            const downloadLinks = Array.from(document.querySelectorAll('.download_icon'));

            downloadLinks.forEach(link => {
                // Create a temporary anchor element
                const tempLink = document.createElement('a');
                tempLink.href = link.href;
                tempLink.download = ''; // Use the filename from the href attribute if needed
                tempLink.style.display = 'none'; // Hide the link

                // Append to the body
                document.body.appendChild(tempLink);
                tempLink.click(); // Trigger the download
                document.body.removeChild(tempLink); // Remove from the DOM
            });
        });
    });
</script>
<script>
    $(document).ready(function () {
        const csrfToken = $('meta[name="csrf-token"]').attr("content");

        function updateSection(itemId, sectionId) {
            $.ajax({
                url: "{{ route('organization.sections.drag.update') }}",
                type: "POST",
                data: {
                    item_id: itemId,
                    section_id: sectionId,
                    _token: csrfToken
                },
                success: function (response) {
                    console.info("AJAX Success:", response);
                },
                error: function (xhr, status, error) {
                    console.error("AJAX Error:", error);
                }
            });
        }

        // Handle AJAX on sort update (covers all containers)
        $(".main-table-dropable tbody, .dropable-content-here tbody").sortable({
            connectWith: ".main-table-dropable tbody, .dropable-content-here tbody",
            placeholder: "sortable-placeholder",
            handle: ".drag-handle",
            helper: function (e, tr) {
                const $original = tr.clone();
                $original.css("width", tr.width());
                return $original;
            },
            start: function (event, ui) {
                console.time("drag here");
                ui.item.addClass("dragging");
            },
            stop: function (event, ui) {
                ui.item.removeClass("dragging here");
                console.timeEnd("drag");

                const itemId = ui.item.data("item-id");
                const $newContainer = ui.item.closest(".main-table-dropable, .dropable-content-here");
                const newSectionId = $newContainer.data("sec-id") || $newContainer.data("section-id") || null;

                updateSection(itemId, newSectionId);
            }
        });

        // Optional: hover effects and section expansion
        $(".dropable-content-here, .main-table-dropable").droppable({
            accept: "tr:not(.section-header)",
            hoverClass: "hovered",
            over: function () {
                $(this).addClass("hovered");
                const sectionId = $(this).data("section-id");
                if (sectionId) {
                    $(`#section-${sectionId}`).collapse("show");
                }
            },
            out: function () {
                $(this).removeClass("hovered");
            },
            // ❌ NO `drop` needed here if sortable handles movement
        });
    });
</script>
<script>
    function randUnique(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    function saveSection(uni, isEdit = false) {
        // alert(uni)
        var opportunityId = {{$opportunity->id}};
        var sectionName = $('#editinput-' + uni).val();
        // var sectionId = isEdit ? $('#section-id-' + uni).val() : null; // Check if we are editing
        sectionId = isEdit ? $('#section-id-' + uni).val() : null;

        if (!sectionName || sectionName.trim() === "") {
            sectionName = "Section"; // Default value
        }
        window.sectionId = isEdit ? $('#section-id-' + uni).val() : null;

        $.ajax({

            url: "{{ route('organization.sections.save') }}",
            type: 'POST',
            data: {
                opportunity_id: opportunityId,
                section_name: sectionName,
                section_id: sectionId,
                _token: '{{ csrf_token() }}'
            },
            success: function (response) {
                window.sectionId = response.section_id;
                uni = response.section_id;
                $(".editinput").css('display', 'none');
                $('#editinput-' + uni).css('display', 'inline-block');

                $(".sectionNames").css('display', 'inline-block');
                $('#section1Heading-' + uni).css('display', 'none');

                $(".okbutn").css('display', 'none');
                $('#okbutn-' + uni).css('display', 'inline-block');

                $(".editbutn").css('display', 'inline-block');
                $('#editbutn-' + uni).css('display', 'none');

                // Handle success (e.g., update UI or add new section)
                var ddd = `<tr class="tablerows-${uni} lastforsectionrow" style="background: #f5faff">
            <td>
            <div class="d-flex align-items-center">
            <input type="checkbox"
            class="dynamic-checkbox"
            id="checkbox-${uni}"
            data-id="${uni}" style="cursor:pointer;"></div>
            </td>                                <td class="align-middle">
            <div class="d-flex align-items-center">
            <div id="section1Heading-${uni}" class="me-2 sectionNames" style="display: none;">Section</div>
            <input type="hidden" style="text-align: left;" class="editinputid" id="editinputid-${uni}" data-sec-id ="${uni}" value="${uni}">
            <input type="text" style="text-align: left; display: inline-block" class="editinput" placeholder="Enter Section Name" id="editinput-${uni}">
            <a href="#" onclick="editSection2(${uni})" id="editbutn-${uni}" class="text-decoration-none mt-1 mx-3 editbutn" style="display: none">
            <svg
            class=""
            width="15"
            height="15"
            viewBox="0 0 22 22"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            >
            <path
                d="M1.87604 17.1156C1.92198 16.7021 1.94496 16.4954 2.00751 16.3022C2.06301 16.1307 2.14143 15.9676 2.24064 15.8171C2.35246 15.6475 2.49955 15.5005 2.79373 15.2063L16 2C17.1046 0.895427 18.8955 0.895428 20 2C21.1046 3.10457 21.1046 4.89543 20 6L6.79373 19.2063C6.49955 19.5005 6.35245 19.6475 6.18289 19.7594C6.03245 19.8586 5.86929 19.937 5.69785 19.9925C5.5046 20.055 5.29786 20.078 4.88437 20.124L1.5 20.5L1.87604 17.1156Z"
                stroke="#7C8091"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
            </svg>
            </a>
            <a href="#" onclick="updateSection3(${uni})" style="padding-right: 25px; display: inline-block" id="okbutn-${uni}" class="btn btn-sm btn-primary text-decoration-none mt-1 mx-3 okbutn">
            Save
            </a>
            </div>
            </td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td class="d-flex justify-content-center" style="gap: 15px">
            <div class="dropdown">
            <button
            class="btn btn-sm bg-transparent dropdown-toggle"
            type="button"
            data-bs-toggle="dropdown"
            aria-expanded="false"
            >
            <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="currentColor"
            class="bi bi-three-dots"
            viewBox="0 0 16 16"
            >
            <path
            d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"
            />
            </svg>
            </button>
            <ul class="dropdown-menu">
            <li>
            <a onclick="editSection2(${uni})" class="dropdown-item" href="#">Edit</a>
            </li>
            <li>
            <a onclick="deleteSection3(${uni})" class="dropdown-item" href="#">Delete</a>
            </li>
            </ul>
            </div>
            <button
            class="btn btn-sm collapsed"
            id="mainbtnnow-${uni}"
            style="background: #f6f6f6; border: 1px solid #dfdfdf;"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#section-${uni}"
            aria-expanded="false"
            aria-controls="section-initial"
            onclick="toggleAccordion2(${uni})"
            >
            <label for="" class="changeiconall changeicon-${uni}">
            <svg
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            fill="currentColor"
            class="bi bi-chevron-down arrow-icon "
            viewBox="0 0 16 16"
            >
            <path
            fill-rule="evenodd"
            d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"
            />
            </svg>
            </label>
            </button>
            </td>
            </tr>
            <tr id="section-${uni}" class="collapse tavy tablerows-${uni}">
            <td style="padding: 0px !important" colspan="10">
            <table class="table">
            <tbody class="sortable-content accord-bodys accord-bodys-${uni}">

            </tbody>
            </table>
            </td>
            </tr>`;
                $('.maintable').prepend(ddd);
            },
            error: function (xhr) {
                // Handle error
                alert(xhr.responseJSON.message);
            }
        });
    }

    function SectionUpdate(uni, isEdit = false) {
        var opportunityId = {{$opportunity->id}}; // Assuming you have an opportunity_id field
        var sectionName = $('#editinput-' + uni).val(); // Get the section name input value
        // var sectionId = isEdit ? $('#section-id-' + uni).val() : null; // Check if we are editing
        sectionId = isEdit ? $('#section-id-' + uni).val() : null;
        // alert(window.sectionId);

        // If the section name is empty or undefined, set a default value of "Section"
        if (!sectionName || sectionName.trim() === "") {
            sectionName = "Section"; // Default value
        }
//    var sectionId = isEdit ? $('#section-id-' + uni).val() : null; // Check if we are editing
        // sectionId = isEdit ? $('#section-id-' + uni).val() : null;
        $.ajax({

            url: "{{ route('organization.sections.save') }}",
            type: 'POST',
            data: {
                opportunity_id: opportunityId,
                section_name: sectionName,
                section_id: uni, // Pass section_id only if editing
                _token: '{{ csrf_token() }}'
            },
            success: function (response) {
                // alert(response.section_id);
                console.info(response);
                window.sectionId = response.section_id;
                $('#section1Heading-' + uni).text(sectionName); // Update the section heading to the new name
                $('#editinput-' + uni).hide(); // Hide the input field after update
                $('#okbutn-' + uni).hide(); // Hide the save button
                $('#editbutn-' + uni).show(); // Show the edit button (icon)
                $('#section1Heading-' + uni).show(); // Show the section name
                // Handle success (e.g., update UI or add new section)
                // alert(response.message);
            },
            error: function (xhr) {
                // Handle error
                alert(xhr.responseJSON.message);
            }
        });
    }

    // Example usage for adding a section
    function addSection() {
        var uni = randUnique(1000, 9999);
        saveSection(uni, false); // Pass false for adding a section
    }

    // Example usage for editing a section
    function updateSection3(uni) {
        SectionUpdate(uni, true); // Pass true for editing a section
    }

    function editSection() {
        // alert('section edit');
        $('#section1Heading').css('display', 'none');
        $('#editbutn').css('display', 'none');
        $('#editinput').css('display', 'inline-block');
        $('#okbutn').css('display', 'inline-block');
        var inupt = $('#section1Heading').text();
        $('#editinput').val(inupt);

    }

    function updateSection() {
        // alert('section edit');
        $('#section1Heading').css('display', 'inline-block');
        $('#editbutn').css('display', 'inline-block');
        $('#editinput').css('display', 'none');
        $('#okbutn').css('display', 'none');
        var inupt = $('#editinput').val();
        $('#section1Heading').text(inupt);

    }

    function editSection2(c) {
        // alert('section edit');
        $('#section1Heading-' + c).css('display', 'none');
        $('#editbutn-' + c).css('display', 'none');
        $('#editinput-' + c).css('display', 'inline-block');
        $('#okbutn-' + c).css('display', 'inline-block');
        var inupt = $('#section1Heading-' + c).text();
        $('#editinput-' + c).val(inupt);

    }

    function updateSection2(c) {
        // alert('section edit');
        $('#section1Heading-' + c).css('display', 'inline-block');
        $('#editbutn-' + c).css('display', 'inline-block');
        $('#editinput-' + c).css('display', 'none');
        $('#okbutn-' + c).css('display', 'none');
        var inupt = $('#editinput-' + c).val();
        $('#section1Heading-' + c).text(inupt);

    }

    function deleteSection() {
        // alert('delete section');
        $('.tablerows').remove();
    }

    function deleteSection2(c) {
        // alert('delete section');
        $('.tablerows-' + c).remove();
    }

    function deleteSection3(c, opportunityId) {
        var opp_id = "{{$opportunity->id}}"
        $.ajax({
            url: "{{ route('organization.sections.delete') }}",  // Replace with your server endpoint URL
            type: 'POST',
            data: {
                section_id: c,              // Sending 'c' as section_id
                opportunityId: opp_id  // Sending opportunityId
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Include CSRF token for Laravel
            },
            success: function (response) {
                console.info(response);

                location.reload();
                // Optionally remove deleted section row from the table
                $(`.tablerows-${c}`).remove();
            },
            error: function (xhr, status, error) {
                // Handle error response
                console.error('Error:', error);
            }
        });
    }


    function deleteItemDB(rowId, c) {
        // alert(rowId,c)
        $.ajax({
            url: "{{ route('organization.items.delete') }}",  // Replace with your server endpoint URL
            type: 'POST',
            data: {
                rowId: rowId,              // Sending 'c' as section_id
                // Sending opportunityId
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Include CSRF token for Laravel
            },
            success: function (response) {
                // Handle success response
                console.log(response.message);
                // Optionally remove deleted section row from the table
                $(`.datarowsequ-${c}`).remove();
            },
            error: function (xhr, status, error) {
                // Handle error response
                console.error('Error:', error);
            }
        });
    }

    function deletedatarowsequ2(c, tcosttype, TJQ, tcost, type) {
        // alert('delete section');
        var opp_id = "{{$opportunity->id}}"
        $.ajax({
            url: "{{ route('organization.estimateitems.delete') }}",  // Replace with your server endpoint URL
            type: 'POST',
            data: {
                id: c,
                type: type,
                opp_id: opp_id,
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') //
            },
            success: function (response) {
                // Handle success response
                console.log(response.message);
                location.reload();
                // Optionally remove deleted section row from the table
                $('.datarowsequ22-' + c).remove();
                $('#equipmentplusbtn-' + c).removeAttr('disabled');
                $('#equipmentplusbtn-' + c).removeClass('disabledbtn');
                var ttl = sumEquipmentCount(TJQ);


                $('#totalcountequip').text('$' + ttl);

                var ttlp = response.data;


                $('#totalcountequipPrice').text('$' + ttlp);
            },
            error: function (xhr, status, error) {
                // Handle error response
                console.error('Error:', error);
            }


        });

        // $('.equc-'+c).css('stroke', '#D3D6E0');
    }

    function deletedatarowsContractor(c, tcost) {
        // alert('delete section');
        $('.datarowsContractor-' + c).remove();
        $('#contractorplusbtn-' + c).removeAttr('disabled');
        $('#contractorplusbtn-' + c).removeClass('disabledbtn');
        var ttl = sumEquipmentCount('subcontcount');
        $('#totalSubContractorsValu').text('$' + ttl);
        var ttlp = sumEquipmentCount('contractortotalprice');
        $('#totalSubContractorsValuPrice').text('$' + ttlp.toFixed(2));
        // $('.equc-'+c).css('stroke', '#D3D6E0');
    }

    function deletedatarowsadded(c, mclass, category_list) {
        // alert('delete section');
        $('.' + mclass + '-' + c).remove();
        if (category_list == "equipment") {
            var ttl = sumEquipmentCount('equipmentcount');
            $('#totalcountequip').text('$' + ttl);
        } else if (category_list == "labors") {
            var ttl = sumEquipmentCount('laborcount');

            let sum = 0;

// Get all inputs with the class 'totalhourquantity'
            $('.totalhourquantity').each(function () {
                let value = parseFloat($(this).val()) || 0; // Convert value to a number, handle NaN
                sum += value;
            });
// var thuq=parseFloat(sum) + parseFloat(qu);
            $('#totalhourslabors').text(sum);
            $('#totalcountlabor').text('$' + ttl);
        } else if (category_list == "hard_materials") {
            var ttl = sumEquipmentCount('materialcount');
            $('#totalcountmaterial').text('$' + ttl);
        } else if (category_list == "plant_materials") {
            var ttl = sumEquipmentCount('materialcount');
            $('#totalcountmaterial').text('$' + ttl);
        } else if (category_list == "other_costs") {
            var ttl = sumEquipmentCount('othercostcount');
            $('#totalcountother').text('$' + ttl);
        } else if (category_list == "contractors") {
            var ttl = sumEquipmentCount('subcontcount');
            $('#totalSubContractorsValu').text('$' + ttl);
        }
    }

    function deletedatarowshard(c) {
        // alert('delete section');
        $('.datarowshard-' + c).remove();
        $('#hardmplusbtn-' + c).removeAttr('disabled');
        $('#hardmplusbtn-' + c).removeClass('disabledbtn');
        var ttl = sumEquipmentCount('materialcount');
        $('#totalcountmaterial').text('$' + ttl);
        var ttlp = sumEquipmentCount('materialtotalprice');
        $('#totalcountmaterialPrice').text('$' + ttlp.toFixed(2));
        // $('.hardm-'+c).css('stroke', '#D3D6E0');
    }

    function deletedatarowsplant(c) {
        // alert('delete section');
        $('.datarowsplant-' + c).remove();
        $('#plantmplusbtn-' + c).removeAttr('disabled');
        $('#plantmplusbtn-' + c).removeClass('disabledbtn');
        var ttl = sumEquipmentCount('materialcount');
        $('#totalcountmaterial').text('$' + ttl);
        var ttlp = sumEquipmentCount('materialtotalprice');
        $('#totalcountmaterialPrice').text('$' + ttlp.toFixed(2));
        // $('.plantm-'+c).css('stroke', '#D3D6E0');
    }

    function deletedatarowsother(c) {
        // alert('delete section');
        $('.datarowsother-' + c).remove();
        $('#otherplusbtn-' + c).removeAttr('disabled');
        $('#otherplusbtn-' + c).removeClass('disabledbtn');
        var ttl = sumEquipmentCount('othercostcount');
        $('#totalcountother').text('$' + ttl);
        var ttlp = sumEquipmentCount('othertotalprice');
        $('#totalcountotherPrice').text('$' + ttlp.toFixed(2));
        // $('.ocosts-'+c).css('stroke', '#D3D6E0');
    }

    function deletedatarowslabor(c) {
        // alert('delete section');
        $('.datarowsLabor-' + c).remove();
        var ttl = sumEquipmentCount('laborcount');
        $('#laborplusbtn-' + c).removeAttr('disabled');
        $('#laborplusbtn-' + c).removeClass('disabledbtn');
        $('#totalcountlabor').text('$' + ttl);
        var ttl2 = sumEquipmentCount('burdencount');

        var ttlp = sumEquipmentCount('labortotalprice');
        $('#totalcountlaborPrice').text('$' + ttlp.toFixed(2));
        // alert(totalburden);
        //       var ty2=parseFloat(totalburden) + parseFloat(ttl2);
        var bdn = ttl2.toFixed(2);
        $('#totalcountburden').text('$' + bdn);

        let sum = 0;

// Get all inputs with the class 'totalhourquantity'
        $('.totalhourquantity').each(function () {
            let value = parseFloat($(this).val()) || 0; // Convert value to a number, handle NaN
            sum += value;
        });
// var thuq=parseFloat(sum) + parseFloat(qu);
        $('#totalhourslabors').text(sum);
        // $('.laborsvg-'+c).css('stroke', '#D3D6E0');
    }
</script>
<script>
    const accordion = document.querySelector('.accordion');

    accordion.addEventListener('click', (e) => {
        if (e.target.matches('.tab__label')) {
            const tab = e.target.closest('.tab');
            const activeTab = accordion.querySelector('.tab.active');

            if (activeTab && activeTab !== tab) {
                activeTab.classList.remove('active');
            }

            tab.classList.toggle('active');
        }
    });

    let tempData = [];  // This will hold all the added/updated items
    var opportunityId = {{ $opportunity->id }};

    function addItemToTemp(itemType, id, quantity, uom, total_cost, gross_margin) {

        // alert(gross_margin)
        // Define a unique identifier based on the section type
        const itemId = itemType === 'labors' ? `${id}` :
            itemType === 'equipment' ? `${id}` :
                itemType === 'hard_materials' ? `${id}` :
                    itemType === 'plant_materials' ? `${id}` :
                        itemType === 'contractors' ? `${id}` :
                            `${id}`;

        // Check if sectionId exists; if not, set it to null
        const sectionId = typeof window.sectionId !== 'undefined' ? window.sectionId : null;

        // Check if the item with the same unique identifier already exists in tempData
        const existingItemIndex = tempData.findIndex(item => item.itemId === itemId);

        if (existingItemIndex !== -1) {
            // Update existing item if found
            tempData[existingItemIndex] = {
                itemId: itemId,
                quantity: quantity,
                total_cost: total_cost,
                gross_margin: gross_margin,
                section_id: sectionId, // Section ID handled here
                uom: uom,
                opportunityId: opportunityId,
                itemType: itemType
            };
        } else {
            // Push the new item (as an object) into the tempData array
            tempData.push({
                itemId: itemId,
                quantity: quantity,
                total_cost: total_cost,
                gross_margin: gross_margin,
                section_id: sectionId, // Section ID handled here
                uom: uom,
                opportunityId: opportunityId,
                itemType: itemType
            });
        }

        // Optionally log the current state of tempData
        console.log("Temporary data updated: ", tempData);
    }


    var opportunityId = {{ $opportunity->id }};

    function saveBatchItems(itemType, id, quantity, uom, total_cost, gross_margin, uc, up, tp, section_name, depth='',sqft='', callback) {
        console.log('obj',{itemType, id, quantity, uom, total_cost, gross_margin, uc, up, tp, section_name, depth,sqft});
        var tempData = {
            itemType: itemType,
            id: id,
            uom: uom,
            total_cost: total_cost,
            gross_margin: gross_margin,
            quantity: quantity,
            opportunityId: opportunityId,
            uc: uc,
            up: up,
            tp: tp,
            section_name: section_name,
            depth: depth,
            sqft: sqft,
        };


        $.ajax({
            method: "POST",
            url: "{{ route('organization.estimate.add-items-batch') }}",  // Update to your route
            data: {
                items: tempData,  // Send the whole tempData array to the backend
                _token: '{{ csrf_token() }}' // Include CSRF token for Laravel
            },
            success: function (response) {
                console.info("All items saved successfully.......:", response.data);
                if (itemType == 'equipment') {
                    $('.getitemsequipid-' + id).val(response.data.id);
                } else if (itemType == 'labors') {
                    $('.getitemsid-' + id).val(response.data.id);
                } else if (itemType == 'hard_materials') {
                    $('#hard_material_id').val(response.data.id);
                    $('#hard_material_uom').val(response.data.uom);
                    $('.getitemshardid-' + id).val(response.data.id);
                } else if (itemType == 'plant_materials') {
                    $('.getitemsplantid-' + id).val(response.data.id);
                } else if (itemType == 'other_costs') {
                    $('.getitemsotherid-' + id).val(response.data.id);
                } else if (itemType == 'contractors') {
                    $('.contractors-' + id).val(response.data.id);
                }
                // Clear tempData after saving
                console.info("All items saved successfully:");
                tempData = [];
                if (callback && typeof callback === 'function') {
                    callback(response.data);
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error("Error saving batch items:", textStatus, errorThrown);
                console.info("All items saved successfully: Error");
            }
        });
    }


    function updateEstimateItems(id, qu, gm, uc, totalcosts, up, tp) {
        var tempData = {
            id: id,
            total_cost: totalcosts,
            gross_margin: gm,
            quantity: qu,
            uc: uc,
            up: up,
            tp: tp,
        };


        $.ajax({
            method: "POST",
            url: "{{ route('organization.estimate.update-items-batch') }}",  // Update to your route
            data: {
                items: tempData,  // Send the whole tempData array to the backend
                _token: '{{ csrf_token() }}' // Include CSRF token for Laravel
            },
            success: function (response) {
                console.info(response);
                $('#quantityequ-' + id).val(response.data.quantity);
                $('#totalcostsequip-' + id).text(response.data.total_cost);
                $('#grossmarginequ-' + id).val(response.data.gross_margin);
                $('#totalpriceequi-' + id).text(response.data.total_price);

            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error("Error saving batch items:", textStatus, errorThrown);
                console.info("All items saved successfully: Error");
            }
        });
    }


    function updateEstimateItems2(id, qu, gm, uc, totalcosts, up, tp) {
        console.info(id);
        var tempData = {
            id: id,
            total_cost: totalcosts,
            gross_margin: gm,
            quantity: qu,
            uc: uc,
            up: up,
            tp: tp,
        };


        $.ajax({
            method: "POST",
            url: "{{ route('organization.estimate.update-items-batch') }}",  // Update to your route
            data: {
                items: tempData,  // Send the whole tempData array to the backend
                _token: '{{ csrf_token() }}' // Include CSRF token for Laravel
            },
            success: function (response) {
                console.info(response);
                $('#quantityequ2-' + id).val(response.data.quantity);
                $('#totalcostsequip2-' + id).text(response.data.total_cost);
                $('#grossmarginequ2-' + id).val(response.data.gross_margin);
                $('#totalpriceequi2-' + id).text(response.data.total_price);

            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error("Error saving batch items:", textStatus, errorThrown);
                console.info("All items saved successfully: Error");
            }
        });
    }

    $(document).on('click','.getDetailsOfItem',function () {
        const itemId = $(this).attr('data-id');
        const baseUrl = "{{ route(getRouteAlias() . '.estimate.get-estimate-item-by-id', ['id' => '__ID__']) }}";
        const url = baseUrl.replace('__ID__', itemId);
        $.ajax({
            url: url,
            method: 'GET',
            success: function (response) {
                if(response) {
                    const data = response?.data;
                    $('#hard_material_id').val(data?.id);
                    $('#depthVal').val(data?.depth);
                    $('#sqftVal').val(data?.sqft);
                    $('#inputModal').modal('show');
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error("Error saving batch items:", textStatus, errorThrown);
                console.info("All items saved successfully: Error");
            }
        });
    })

    $(document).on('click','#updateEstimateItem',function () {
        /*const category_list = $('#edit_category_list').val();
        const item_name = $('#edit_item_name').val();
        const item_quantity = $('#edit_item_quantity').val();
        const item_unitcost = $('#edit_item_unitcost').val();
        const item_uom = $('#edit_item_uom').val();*/
        const itemId = $('#hard_material_id').val();
        const depth = $('#depthVal').val();
        const sqft = $('#sqftVal').val();
        const baseUrl = "{{ route(getRouteAlias() . '.estimate.hard-item-update') }}";
        $.ajax({
            url: baseUrl,
            method: 'POST',
            data: {
                depth: depth,
                sqft: sqft,
                id: itemId,
                _token: '{{ csrf_token() }}'
            },
            success: function (response) {
                if(response) {
                    //toastr.success(response.message);
                    $('#editItemModal').modal('hide');
                    window.location.reload();
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error("Error saving batch items:", textStatus, errorThrown);
                console.info("All items saved successfully: Error");
            }
        });
    });

    $(document).on('change', '#category_list', function () {
        const selectedCategory = $(this).val();
        let url = "{{ route('organization.material.uom', ['type' => ':type']) }}";
        url = url.replace(':type', selectedCategory);

        if (selectedCategory) {
            $.ajax({
                url: url,
                method: 'GET',
                success: function (response) {
                    if (response) {
                        $('#item_uom').empty().append(`<option value="">Choose...</option>`);

                        $.each(response?.units, function (index, value) {
                            $('#item_uom').append(`<option value="${value?.name}">${value?.name}</option>`);
                        });
                    }
                },
                error: function (error) {
                    console.log('error', error);
                }
            });
        }
    });

</script>
{{--@endpush--}}
