@extends('email_template.client.layouts.master')
@section('max-width', '640px')
@section('content')
    <tr style=" display:block; background-color: {{ $payload['primary_color'] ?? '#003366'  }}; padding: 0 48px 0 45px;">
        <td style="width: 84px; vertical-align: middle; padding: 36px 0 39px 0;">
            <img height="92px" width="164px"
                src="{{ isset($payload['companyimage']) ? $payload['companyimage'] ?? asset('admin_assets/images/elmos-logo.png') : asset('admin_assets/images/elmos-logo.png') }}"
                alt="elmos logo">
        </td>

    </tr>
    <!-- reset-your-email -->
    <tr>
        <td class="pp-0" style="max-width: 554px; width: 100%;background-color: #EDEDED; padding:16px 48px 30px; ">
            <table class="table w-100" style="width: 100%;">
                <tr>
                    <td class="pp-10"
                        style="background: #FFFFFF; border-radius: 4px; padding: 10px 40px; max-width: 554px;width: 100%;">
                        <table>
                            <tbody>
                                <tr>
                                    <td>
                                        <h1
                                            style="font-family: 'Open Sans', sans-serif;  font-style: normal; font-weight: 700;  font-size: 24px; line-height: 140%; color: #192A3E;">
                                            Your {{ config('app.name') }} Password Has Been Changed</h1>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <p
                                            style="font-family: 'Open Sans', sans-serif;  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683;">
                                            Hi<br><br>
                                            We are writing to confirm that your {{ config('app.name') }} password has been
                                            successfully
                                            changed. If you did not make this change, please contact our support
                                            team immediately.</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <p
                                            style=" font-family: 'Open Sans', sans-serif; font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683;">
                                            We take the security of your {{ config('app.name') }} account very seriously,
                                            and we
                                            recommend that you choose a strong password and keep it confidential. If
                                            you have any concerns about the security of your account, please do not
                                            hesitate to reach out to us.
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <p
                                            style=" font-family: 'Open Sans', sans-serif; font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 10px;">
                                            Thank you
                                        </p>
                                        <p
                                            style=" font-family: 'Open Sans', sans-serif; font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 10px;">
                                            Best regards, <br>
                                            @include('email_template.client.layouts.details_partial')
                                        </p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </td>
                </tr>
            </table>
        </td>
    </tr>
@endsection
