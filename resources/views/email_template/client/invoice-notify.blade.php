@extends('email_template.client.layouts.master')
@section('title', 'Estimate Action Notify')
@section('max-width', '640px')
@section('content')

<tr style=" display:block; background-color: {{ $payload['primary_color'] ?? '#003366'  }}; padding: 0 48px 0 45px;">
    <td style="vertical-align: middle; padding: 36px 0 39px 0;">
        @if($payload['org_image'])
                <td style="width: 84px; vertical-align: middle; padding: 36px 0 39px 0;">
                    <img style="width: 84px; height: 44px;" src="{{ $payload['org_image'] }}" alt="elmos-log-img">
                </td>
        @elseif($payload['company_name'])
            <td style="vertical-align: middle; padding: 36px 0 39px 0;">
                <strong style="color: {{ $payload['primary_color'] ?? '#003366'  }};"> {{ $payload['company_name'] }} </strong>
            </td>
        @endif
    </td>
</tr>
<tr>
    <td style="width:100%;background-color:#ededed;padding:16px 48px 30px">
        <table style="margin-right:0px;width:100%">
            <tbody>
                <tr>
                    <td style="background:#ffffff;border-radius:4px;padding:40px;max-width:554px;width:100%"><span
                            class="im">
                            {!! $payload['message'] !!}
                            <a href=" {{ $payload['invoice_url'] }}"
                                style="background: {{ $payload['primary_color'] ?? '#003366'  }}; border-radius: 6px; font-style: normal; font-weight: 500;font-size: 16px; line-height: 24px;padding: 17px 31px; color: #FFFFFF; border: none;margin-top: 10px; text-decoration: none;display: inline-block;">
                                View Invoice</a>
                    </td>
                </tr>
            </tbody>
        </table>
    </td>
</tr>
@endsection
