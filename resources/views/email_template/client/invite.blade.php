@extends('email_template.client.layouts.master')
@section('max-width', '640px')
@section('content')
    <tr
        style=" display:block; background-color: {{ $payload['primary_color'] ?? ($primary_color ?? '#003366') }}; padding: 0 48px 0 45px;">
        <td style="width: 84px; vertical-align: middle; padding: 36px 0 39px 0;">
            @if ($org_image)
                <img style="width: 84px; height: 44px;" src="{{ $org_image }}" alt="elmos-log-img">
            @else
                <strong style="color: white"> {{ $org_name ?? '' }} </strong>
            @endif
        </td>
    </tr>
    <!-- reset-your-email -->
    <tr>
        <td class="pp-0" style="max-width: 554px; width: 100%;background-color: #EDEDED; padding:16px 48px 30px; ">
            <table>
                <tr>
                    <td class="pp-10"
                        style="background: #FFFFFF; border-radius: 4px; padding: 40px; max-width: 554px;width: 100%;">
                        <h1
                            style="  font-style: normal; font-weight: 700;  font-size: 24px; line-height: 140%; color: #192A3E;">
                            Login with your email</h1>

                        <p
                            style="  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 24px;">
                            Hi {{ isset($name) ? $name : '' }},<br><br>
                            We are excited to invite you to join our {{ $org_name ?? '' }}. As a
                            client, you will have access
                            to
                            all of the tools and features that our system has to offer.<br><br>
                            To get started, please follow these steps:
                        <ol
                            style="  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 24px;">
                            <li> Click on the link below to login to your account</li>
                            @if (isset($password))
                                <li
                                    style="font-family: 'Poppins';font-style: normal;font-weight: 400;font-size: 16px; line-height: 150%; color: #707683;">
                                    Enter your email( <b>{{ $email ?? '' }}</b> ) and type password
                                    <b> {{ $password ?? '' }} </b>
                                </li>
                            @else
                                <li
                                    style="font-family: 'Poppins';font-style: normal;font-weight: 400;font-size: 16px; line-height: 150%; color: #707683;">
                                    Enter your email({{ $email ?? '' }} ) to continue using
                                    {{ config('app.name') }}
                                    Dashboard.
                                </li>
                            @endif
                            <li
                                style="font-family: 'Poppins';font-style: normal;font-weight: 400;font-size: 16px; line-height: 150%; color: #707683;">
                                Follow the prompts to complete the registration process.</li>
                        </ol>

                        </p>
                        <p
                            style="  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 24px;">
                            Once you have created your account, you will have full access to our system. If you
                            have any questions or need assistance at any point, please don't hesitate to contact
                            our customer support team.<br><br>

                            Thank you for joining our community, and we look forward to working with
                            you!<br><br>

                            Regards,<br>
                            {{ $org_name ?? '' }}
                        </p>
                        <a href=" {{ $url }}"
                            style="background: {{ $payload['primary_color'] ?? ($primary_color ?? '#003366') }}; border-radius: 6px; font-style: normal; font-weight: 500;font-size: 16px; line-height: 24px;padding: 17px 31px; color: #FFFFFF; border: none;margin-top: 10px; text-decoration: none;display: inline-block;">
                            Log In</a>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
@endsection
