@extends('email_template.client.layouts.master')
@section('max-width', '640px')
@section('content')
    <tr>
        <td>
            <div
                style="display:block; background-color: {{ $payload['primary_color'] ?? '#003366' }}; padding: 0 48px 0 45px;">
                <img height="92px" width="164px"
                    src="{{ isset($payload['companyimage']) ? $payload['companyimage'] ?? asset('admin_assets/images/elmos-logo.png') : asset('admin_assets/images/elmos-logo.png') }}"
                    alt="elmos logo">
            </div>
            <div style="background: #FFFFFF; box-shadow: 0px 0px 16px rgba(0, 0, 0, 0.08);">
                <h1
                    style="font-style: normal; font-weight: 700; font-size: 24px; line-height: 140%; color: #192A3E; padding: 30px 0px 0px 32px;">
                    Password Reset Email!</h1>
                <div style="padding: 31px 28px 56px 32px;">
                    <p style="font-style: normal; font-weight: 400; font-size: 12px; line-height: 15px; color: #2B2B2B;">
                        Hi</p>
                    <p style="font-style: normal; font-weight: 400; font-size: 12px; line-height: 15px; color: #2B2B2B;">
                        You've requested a password reset for your account. To proceed, click on the link below and enter a
                        new password.
                    </p>
                    {{-- <p style="font-style: normal; font-weight: 400; font-size: 12px; line-height: 15px; color: #2B2B2B;">
                        You can now view your information and instruction how to change your account settings.</p> --}}
                    <p style="font-style: normal; font-weight: 400; font-size: 12px; line-height: 15px; color: #2B2B2B;">
                        {{-- If the changes described above are accurate, no further action is needed.
                        If anything doesn’t look right, follow the link to make changes. --}}
                        <a href="{{ $payload['url'] ?? '#' }}"
                            style="background: {{ $payload['primary_color'] ?? '#003366' }}; border-radius: 6px; font-style: normal; font-weight: 500; font-size: 16px; line-height: 24px; padding: 8px 16px; color: #FFFFFF; border: none; margin-top: 10px; text-decoration: none; display: inline-block;">Reset
                            your Password</a>
                    </p>
                    <p style="font-style: normal; font-weight: 400; font-size: 12px; line-height: 15px; color: #2B2B2B;">
                        Thank you!</p>
                    @include('email_template.client.layouts.details_partial')
                </div>
            </div>
        </td>
    </tr>
    {{-- <tr>
        <td style="background-color: #003366; padding: 24px; ">
            <div style="display: flex; flex-direction: column; gap: 7px; align-items: center;">
                <div style="display: flex; gap: 24px; justify-content: center; align-items: center;">
                    <a style="font-style: normal; font-weight: 400; font-size: 10px; line-height: 22px; color: #FFFFFF; text-decoration: none;"
                        href="#">Cookies policy</a>
                    <a style="font-style: normal; font-weight: 400; font-size: 10px; line-height: 22px; color: #FFFFFF; text-decoration: none;"
                        href="#">Privacy policy</a>
                    <a style="font-style: normal; font-weight: 400; font-size: 10px; line-height: 22px; color: #FFFFFF; text-decoration: none;"
                        href="#">Terms & conditions</a>
                </div>
                <div>
                    <a style="font-style: normal; font-weight: 400; font-size: 10px; line-height: 22px; color: #FFFFFF; text-decoration: none;"
                        href="#">
                        © 2023 {{ config('app.name') }}. All rights reserved.
                    </a>
                </div>
            </div>
        </td>
    </tr> --}}
@endsection
