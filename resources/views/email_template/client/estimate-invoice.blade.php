@extends('email_template.client.layouts.master')
@section('max-width', '478px')
@section('content')
    <style>
        @media screen and (max-width: 375px) {
            .pp-0 {
                padding: 0 !important;
            }

            .pp-10 {
                padding: 20px !important;
            }
        }
    </style>

    <tr
        style=" display:block; background-color: {{ $payload['primary_color'] ?? ($primary_color ?? '#003366') }}; padding: 0 48px 0 45px;">
        <td style="width: 84px; vertical-align: middle; padding: 36px 0 39px 0;">
            <img style="width: 57px; height: 20px;" src="https://i.ibb.co/T2JPcc4/elmos-logo-sidebar.png">
        </td>
        <td style=" vertical-align: middle; padding: 36px 0 39px 0; width: 100%; text-align: right;">
            <a href="https://www.linkedin.com/company/onsite-technology-group/" style="display: inline-block;vertical-align: middle;"><img style="width: 24px; height: 24px;"
                    src="https://i.ibb.co/gvB1HSQ/linkedin.png"></a>
            <a href="https://www.facebook.com/profile.php?id=61550531592710&mibextid=LQQJ4d"
                style="padding-left: 24px;padding-right: 24px;display: inline-block;vertical-align: middle;"><img style="width: 24px; height: 23px;"
                    src="https://i.ibb.co/t8X571s/facebook.png"></a>
            <a href="https://www.instagram.com/onsite.ai/?igshid=OGQ5ZDc2ODk2ZA%3D%3D" style="display: inline-block;vertical-align: middle;"><img
                    style="width: 24px; height: 23px;" src="https://i.ibb.co/qskXY0X/instagram.png"></a>


        </td>
    </tr>
    <!-- reset-your-email -->
    <tr>
        <td class="pp-0" style="width: 100%;background-color: #EDEDED; padding:16px 48px 30px; ">
            <table style="margin-right: 0px;width: 100%;">
                <tr>
                    <td class="pp-10"
                        style="background: #FFFFFF; border-radius: 4px; padding: 40px; max-width: 554px;width: 100%;">
                        @if (isset($message))
                            {!! $message !!}

                            <p
                                style="  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 10px;">
                                {{-- If you have any questions, please do not hesitate to contact us at <a href="mailto:{{$payload['company_email'] ?? ''}}">{{ $payload['company_email'] ?? '' }} </a>.
                                <br><br>

                                Regards,<br> --}}
                                {{-- @include('email_template.client.layouts.details_partial') --}}
                            </p>
                        @else
                            <h1
                                style="  font-style: normal; font-weight: 700;  font-size: 24px; line-height: 140%; color: #192A3E;">
                                Estimate</h1>
                            <p
                                style="  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 24px;">
                                Hi {{ $client_name ?? '' }},<br><br>
                                Thank you for the opportunity to provide you with an estimate.
                            </p>

                            <p
                                style="  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 24px;">
                                The estimate total is ${{ $grand_total ?? '' }}
                            </p>

                            <p
                                style="  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 10px;">
                                If you have any questions, please do not hesitate to contact us at
                                {{ $company_email ?? '' }}.
                                <br><br>

                                Regards,<br>
                                @include('email_template.client.layouts.details_partial')
                            </p>
                        @endif


                    </td>
                </tr>
            </table>
        </td>
    </tr>


@endsection
