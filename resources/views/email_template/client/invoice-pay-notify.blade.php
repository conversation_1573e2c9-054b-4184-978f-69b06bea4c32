@extends('email_template.client.layouts.master')
@section('max-width', '640px')
@section('content')
    <tr style=" display:block; background-color: {{ $payload['primary_color'] ?? '#003366'  }}; padding: 0 48px 0 45px;">
        @php
            $invoice=$payload['invoice'];
            $org_image=$payload['org_image'];
            $company_name=$payload['company_name'];
        @endphp
        @if (isset($org_image))
            <td style="width: 84px; vertical-align: middle; padding: 36px 0 39px 0;">
                <img style="width: 84px; height: 44px;" src="{{ $org_image }}" alt="elmos-log-img">
            </td>
        @else
            <td style="vertical-align: middle; padding: 36px 0 39px 0;">
                <strong style="color: #FFFFFF"> {{ optional($invoice?->organization)?->company_name }}
                </strong>
            </td>
        @endif
    </tr>
    <!-- reset-your-email -->
    <tr>
        <td class="pp-0" style="max-width: 554px; width: 100%;background-color: #EDEDED; padding:16px 48px 30px; ">
            <table>
                <tr>
                    <td class="pp-10"
                        style="background: #FFFFFF; border-radius: 4px; padding: 40px; max-width: 554px;width: 100%;">
                        <h1
                            style="  font-style: normal; font-weight: 700;  font-size: 24px; line-height: 140%; color: #192A3E;">
                            Invocie# {{ $invoice?->invoice_number }}</h1>

                        <p
                            style=" font-style: normal; font-weight: 400; font-size: 12px; line-height: 15px; color: #2B2B2B;">
                            Hi <strong>{{ optional($invoice->organization)?->company_name }}</strong> </p>
                        <p
                            style="  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 24px;">
                            <strong> {{ optional($invoice->client)?->company_name }} </strong> has made a
                            payment on invoice <strong> {{ $invoice?->invoice_number }} </strong> in the amount
                            of <strong> {{ $invoice?->total }} </strong> on
                            {{ optional($invoice)?->transaction_date }}.
                        </p>
                        <p
                            style="  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 24px;">
                            PAYMENT SUMMARY:
                        </p>
                        <p
                            style="  font-style: normal; font-weight: 400; font-size: 16px;  line-height: 150%;  color: #707683; padding-top: 10px;">
                            Invoice Number : {{ $invoice->invoice_number }} <br>
                            Amount : {{ $invoice->total }} <br>
                            Transaction Date : {{ optional($invoice)?->transaction_date }}
                            <br>
                            Method : {{ $invoice->payment_method }} <br>
                        </p>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
@endsection
