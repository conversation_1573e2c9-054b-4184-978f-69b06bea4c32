{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1.0", "barryvdh/laravel-dompdf": "^2.2", "doctrine/dbal": "^3.6", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.7", "laravel/framework": "^10.0", "laravel/jetstream": "^3.0", "laravel/sanctum": "^3.0", "laravel/tinker": "^2.7", "livewire/livewire": "*******", "maatwebsite/excel": "^3.1", "propaganistas/laravel-phone": "^5.0", "spatie/laravel-medialibrary": "^10.0.0", "spatie/laravel-permission": "^5.10", "spatie/pdf-to-image": "^1.2", "stripe/stripe-php": "^7.79", "vinkla/hashids": "^11.0", "yajra/laravel-datatables-oracle": "^10.4"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "laravel/telescope": "^5.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/AppConstant.php", "app/Helpers/Functions.php", "app/Helpers/FileUploadManager.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}