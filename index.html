<!-- Bootstrap 5 CDN -->
<link
    href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
    rel="stylesheet"
/>

<!-- Custom Styles -->
<style>
    .bg-card {
        background-color: #ffffff;
    }

    .text-muted-foreground {
        color: #6c757d;
    }

    .text-foreground {
        color: #212529;
    }

    .bg-dashboard-warning {
        background-color: #f59e0b;
    }

    .badge-rounded {
        border-radius: 999px;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .font-semibold {
        font-weight: 600;
    }
    .card-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 11px;
        padding: 2px 8px;
        border-radius: 10px;
        background-color: #0d6efd; /* Bootstrap primary */
        color: #fff;
    }

    .card-content {
        position: relative;
        padding: 1rem;
    }

    .card h6 {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }

    .card .value {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .card .estimate {
        font-size: 0.75rem;
        color: #6c757d;
    }

    .metric-card {
        width: 100%;
        max-width: 220px;
        flex: 1 1 auto;
    }
</style>

<!-- Job Card -->
<div class="bg-card border rounded-lg p-4 p-md-5 mb-5 shadow-sm">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h4 fw-bold text-foreground mb-0">Here Bis The Job Name</h1>
        <span class="badge bg-dashboard-warning text-white badge-rounded"
            >Pending</span
        >
    </div>

    <!-- Job Details Grid: 3 Rows, 4 Columns -->
    <div class="row gy-3 gx-4">
        <!-- Row 1 -->
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Job no#:</div>
            <p class="font-semibold text-foreground mb-0">10269502</p>
        </div>
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Operation Manager:</div>
            <p class="font-semibold text-foreground mb-0">John Doe</p>
        </div>
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Division:</div>
            <p class="font-semibold text-foreground mb-0">Landscape</p>
        </div>
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Service Line:</div>
            <p class="font-semibold text-foreground mb-0">John Doe</p>
        </div>

        <!-- Row 2 -->
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Property Name:</div>
            <p class="font-semibold text-foreground mb-0">Roof Repair</p>
        </div>
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Account Name:</div>
            <p class="font-semibold text-foreground mb-0">John Smith</p>
        </div>
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Scheduled Date:</div>
            <p class="font-semibold text-foreground mb-0">5/29/2023</p>
        </div>
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Completed Date:</div>
            <p class="font-semibold text-foreground mb-0">Amendment</p>
        </div>

        <!-- Row 3 -->
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Salesman:</div>
            <p class="font-semibold text-foreground mb-0">Catherine James</p>
        </div>
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Estimator Name:</div>
            <p class="font-semibold text-foreground mb-0">Catherine James</p>
        </div>
        <div class="col-md-3">
            <div class="text-sm text-muted-foreground">Account Owner:</div>
            <p class="font-semibold text-foreground mb-0">Ahmad Khan</p>
        </div>
        <div class="col-md-3">
            <!-- Empty to preserve grid -->
        </div>
    </div>
</div>

<div class="d-flex flex-wrap gap-3 mb-4">
    <!-- Revenue -->
    <div class="card shadow-sm metric-card">
        <div class="card-content">
            <h6>Revenue</h6>
            <div class="value text-dark">$ 0.0</div>
            <div class="estimate">Est: $30,001</div>
        </div>
    </div>

    <!-- Selling Price -->
    <div class="card shadow-sm metric-card">
        <div class="card-content">
            <h6>Selling Price</h6>
            <div class="value text-dark">$ 500</div>
        </div>
    </div>

    <!-- GP % -->
    <div class="card shadow-sm position-relative metric-card">
        <div class="card-badge">GP %</div>
        <div class="card-content">
            <h6>GP %</h6>
            <div class="value text-primary">0%</div>
            <div class="estimate">Est: 15%</div>
        </div>
    </div>

    <!-- GP$ -->
    <div class="card shadow-sm position-relative metric-card">
        <div class="card-badge">GP$</div>
        <div class="card-content">
            <h6>GP$</h6>
            <div class="value text-primary">$32.12</div>
            <div class="estimate">Est: $30,001</div>
        </div>
    </div>

    <!-- Rev/mh -->
    <div class="card shadow-sm position-relative metric-card">
        <div class="card-badge">Rev/mh</div>
        <div class="card-content">
            <h6>Rev/mh</h6>
            <div class="value text-primary">$32.12</div>
            <div class="estimate">Est: $30,001</div>
        </div>
    </div>
</div>
